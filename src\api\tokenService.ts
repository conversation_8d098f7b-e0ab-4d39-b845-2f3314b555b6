/**
 * Token Service
 * 
 * Handles storage, retrieval, and management of authentication tokens.
 */

// Storage keys
const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

/**
 * Get the access token from storage
 */
export const getAccessToken = (): string | null => {
  return localStorage.getItem(ACCESS_TOKEN_KEY);
};

/**
 * Get the refresh token from storage
 */
export const getRefreshToken = (): string | null => {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

/**
 * Save tokens to storage
 */
export const setTokens = (accessToken: string, refreshToken: string): void => {
  localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
};

/**
 * Update only the access token
 */
export const updateAccessToken = (accessToken: string): void => {
  localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
};

/**
 * Clear all tokens from storage
 */
export const clearTokens = (): void => {
  localStorage.removeItem(ACCESS_TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
};

/**
 * Check if the user is authenticated (has an access token)
 */
export const isAuthenticated = (): boolean => {
  return !!getAccessToken();
};

/**
 * Parse JWT token and get payload
 * Note: This is a simple implementation and doesn't validate the token
 */
export const parseJwt = (token: string): any => {
  try {
    return JSON.parse(atob(token.split('.')[1]));
  } catch (e) {
    return null;
  }
};

/**
 * Check if the access token is expired
 * Returns true if token is expired or invalid
 */
export const isTokenExpired = (token: string): boolean => {
  const decodedToken = parseJwt(token);
  if (!decodedToken) return true;
  
  // Get current time in seconds
  const currentTime = Math.floor(Date.now() / 1000);
  
  // Check if token is expired (with 30 seconds buffer)
  return decodedToken.exp < currentTime + 30;
};

/**
 * Check if the current access token is expired
 */
export const isCurrentTokenExpired = (): boolean => {
  const token = getAccessToken();
  if (!token) return true;
  return isTokenExpired(token);
};

export default {
  getAccessToken,
  getRefreshToken,
  setTokens,
  updateAccessToken,
  clearTokens,
  isAuthenticated,
  isTokenExpired,
  isCurrentTokenExpired
};
