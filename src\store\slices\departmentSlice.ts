import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  Department,
  WorkActivity,
  fetchDepartments as fetchDepartmentsAPI,
  createDepartment as createDepartmentAPI,
  updateDepartment as updateDepartmentAP<PERSON>,
  deleteDepartment as deleteDepartmentAP<PERSON>,
  fetchWorkActivities as fetchWorkActivitiesAPI,
  createWorkActivity as createWorkActivityAPI,
  updateWorkActivity as updateWorkActivityAPI,
  deleteWorkActivity as deleteWorkActivityAPI
} from '@/services/departmentService';

// Define the state interface
interface DepartmentState {
  departments: Department[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: DepartmentState = {
  departments: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchDepartments = createAsyncThunk(
  'departments/fetchDepartments',
  async (_, { rejectWithValue }) => {
    try {
      return await fetchDepartmentsAPI();
    } catch (error) {
      return rejectWithValue('Failed to fetch departments');
    }
  }
);

export const createDepartment = createAsyncThunk(
  'departments/createDepartment',
  async (name: string, { rejectWithValue }) => {
    try {
      const department = await createDepartmentAPI(name);
      if (!department) {
        return rejectWithValue('Failed to create department');
      }
      return department;
    } catch (error) {
      return rejectWithValue('Failed to create department');
    }
  }
);

export const updateDepartment = createAsyncThunk(
  'departments/updateDepartment',
  async ({ id, name }: { id: string; name: string }, { rejectWithValue }) => {
    try {
      const success = await updateDepartmentAPI(id, name);
      if (!success) {
        return rejectWithValue('Failed to update department');
      }
      return { id, name };
    } catch (error) {
      return rejectWithValue('Failed to update department');
    }
  }
);

export const deleteDepartment = createAsyncThunk(
  'departments/deleteDepartment',
  async (id: string, { rejectWithValue }) => {
    try {
      const success = await deleteDepartmentAPI(id);
      if (!success) {
        return rejectWithValue('Failed to delete department');
      }
      return id;
    } catch (error) {
      return rejectWithValue('Failed to delete department');
    }
  }
);

export const fetchWorkActivities = createAsyncThunk(
  'departments/fetchWorkActivities',
  async (departmentId: string, { rejectWithValue }) => {
    try {
      const activities = await fetchWorkActivitiesAPI(departmentId);
      return { departmentId, activities };
    } catch (error) {
      return rejectWithValue('Failed to fetch work activities');
    }
  }
);

export const createWorkActivity = createAsyncThunk(
  'departments/createWorkActivity',
  async ({ departmentId, name }: { departmentId: string; name: string }, { rejectWithValue }) => {
    try {
      const activity = await createWorkActivityAPI(departmentId, name);
      if (!activity) {
        return rejectWithValue('Failed to create work activity');
      }
      return { departmentId, activity };
    } catch (error) {
      return rejectWithValue('Failed to create work activity');
    }
  }
);

export const updateWorkActivity = createAsyncThunk(
  'departments/updateWorkActivity',
  async (
    { departmentId, activityId, name }: { departmentId: string; activityId: string; name: string },
    { rejectWithValue }
  ) => {
    try {
      const success = await updateWorkActivityAPI(departmentId, activityId, name);
      if (!success) {
        return rejectWithValue('Failed to update work activity');
      }
      return { departmentId, activityId, name };
    } catch (error) {
      return rejectWithValue('Failed to update work activity');
    }
  }
);

export const deleteWorkActivity = createAsyncThunk(
  'departments/deleteWorkActivity',
  async ({ departmentId, activityId }: { departmentId: string; activityId: string }, { rejectWithValue }) => {
    try {
      const success = await deleteWorkActivityAPI(departmentId, activityId);
      if (!success) {
        return rejectWithValue('Failed to delete work activity');
      }
      return { departmentId, activityId };
    } catch (error) {
      return rejectWithValue('Failed to delete work activity');
    }
  }
);

// Create the slice
const departmentSlice = createSlice({
  name: 'departments',
  initialState,
  reducers: {
    toggleDepartmentExpanded: (state, action: PayloadAction<string>) => {
      const departmentId = action.payload;
      const department = state.departments.find(dept => dept.id === departmentId);
      if (department) {
        department.expanded = !department.expanded;
      }
    },
    clearDepartmentError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch departments
      .addCase(fetchDepartments.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDepartments.fulfilled, (state, action) => {
        state.isLoading = false;
        // Ensure each department has a workActivities array
        state.departments = action.payload.map(dept => ({
          ...dept,
          workActivities: dept.workActivities || []
        }));
      })
      .addCase(fetchDepartments.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Create department
      .addCase(createDepartment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createDepartment.fulfilled, (state, action) => {
        state.isLoading = false;
        // Ensure the department has an empty workActivities array
        state.departments.push({
          ...action.payload,
          workActivities: action.payload.workActivities || []
        });
      })
      .addCase(createDepartment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Update department
      .addCase(updateDepartment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateDepartment.fulfilled, (state, action) => {
        state.isLoading = false;
        const { id, name } = action.payload;
        const department = state.departments.find(dept => dept.id === id);
        if (department) {
          department.name = name;
        }
      })
      .addCase(updateDepartment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Delete department
      .addCase(deleteDepartment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteDepartment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.departments = state.departments.filter(dept => dept.id !== action.payload);
      })
      .addCase(deleteDepartment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Fetch work activities
      .addCase(fetchWorkActivities.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWorkActivities.fulfilled, (state, action) => {
        state.isLoading = false;
        const { departmentId, activities } = action.payload;
        const department = state.departments.find(dept => dept.id === departmentId);
        if (department) {
          department.workActivities = activities;
        }
      })
      .addCase(fetchWorkActivities.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Create work activity
      .addCase(createWorkActivity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWorkActivity.fulfilled, (state, action) => {
        state.isLoading = false;
        const { departmentId, activity } = action.payload;
        const department = state.departments.find(dept => dept.id === departmentId);
        if (department && department.workActivities) {
          department.workActivities.push(activity);
        }
      })
      .addCase(createWorkActivity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Update work activity
      .addCase(updateWorkActivity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateWorkActivity.fulfilled, (state, action) => {
        state.isLoading = false;
        const { departmentId, activityId, name } = action.payload;
        const department = state.departments.find(dept => dept.id === departmentId);
        if (department && department.workActivities) {
          const activity = department.workActivities.find(act => act.id === activityId);
          if (activity) {
            activity.name = name;
          }
        }
      })
      .addCase(updateWorkActivity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Delete work activity
      .addCase(deleteWorkActivity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteWorkActivity.fulfilled, (state, action) => {
        state.isLoading = false;
        const { departmentId, activityId } = action.payload;
        const department = state.departments.find(dept => dept.id === departmentId);
        if (department && department.workActivities) {
          department.workActivities = department.workActivities.filter(act => act.id !== activityId);
        }
      })
      .addCase(deleteWorkActivity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { toggleDepartmentExpanded, clearDepartmentError } = departmentSlice.actions;

export default departmentSlice.reducer;
