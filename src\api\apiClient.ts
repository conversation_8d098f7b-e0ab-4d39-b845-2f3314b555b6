/**
 * API Client
 * 
 * Provides reusable wrapper functions for making API requests.
 */

import { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import axiosInstance from './axiosInstance';

/**
 * Standard API response format
 */
export interface ApiResponse<T = any> {
  data: T | null;
  error: string | null;
  status: number;
  success: boolean;
}

/**
 * Transform Axios response to standard API response format
 */
const formatResponse = <T>(response: AxiosResponse<T>): ApiResponse<T> => {
  return {
    data: response.data,
    error: null,
    status: response.status,
    success: true,
  };
};

/**
 * Format error response
 */
const formatErrorResponse = (error: AxiosError): ApiResponse => {
  const status = error.response?.status || 500;
  const errorMessage = 
    (error.response?.data as any)?.message || 
    error.message || 
    'An unexpected error occurred';
  
  return {
    data: null,
    error: errorMessage,
    status,
    success: false,
  };
};

/**
 * Make a GET request
 */
export const get = async <T = any>(
  url: string, 
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  try {
    const response = await axiosInstance.get<T>(url, config);
    return formatResponse<T>(response);
  } catch (error) {
    return formatErrorResponse(error as AxiosError);
  }
};

/**
 * Make a POST request
 */
export const post = async <T = any, D = any>(
  url: string, 
  data?: D, 
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  try {
    const response = await axiosInstance.post<T>(url, data, config);
    return formatResponse<T>(response);
  } catch (error) {
    return formatErrorResponse(error as AxiosError);
  }
};

/**
 * Make a PUT request
 */
export const put = async <T = any, D = any>(
  url: string, 
  data?: D, 
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  try {
    const response = await axiosInstance.put<T>(url, data, config);
    return formatResponse<T>(response);
  } catch (error) {
    return formatErrorResponse(error as AxiosError);
  }
};

/**
 * Make a PATCH request
 */
export const patch = async <T = any, D = any>(
  url: string, 
  data?: D, 
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  try {
    const response = await axiosInstance.patch<T>(url, data, config);
    return formatResponse<T>(response);
  } catch (error) {
    return formatErrorResponse(error as AxiosError);
  }
};

/**
 * Make a DELETE request
 */
export const del = async <T = any>(
  url: string, 
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  try {
    const response = await axiosInstance.delete<T>(url, config);
    return formatResponse<T>(response);
  } catch (error) {
    return formatErrorResponse(error as AxiosError);
  }
};

/**
 * Upload a file
 */
export const uploadFile = async <T = any>(
  url: string,
  file: File,
  onUploadProgress?: (progressEvent: any) => void,
  additionalData?: Record<string, any>
): Promise<ApiResponse<T>> => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    // Add any additional data to the form
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }
    
    const response = await axiosInstance.post<T>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    });
    
    return formatResponse<T>(response);
  } catch (error) {
    return formatErrorResponse(error as AxiosError);
  }
};

// Export all API methods
export default {
  get,
  post,
  put,
  patch,
  delete: del,
  uploadFile,
};
