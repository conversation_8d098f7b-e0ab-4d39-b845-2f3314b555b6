import { api } from './apiService';
import { toast } from '@/components/ui/use-toast';

// API endpoints
const DEPARTMENTS_ENDPOINT = '/departments';
const WORK_ACTIVITIES_ENDPOINT = (departmentId: string) => `/departments/${departmentId}/work-activities`;

// Interface definitions
export interface WorkActivity {
  id: string;
  name: string;
  departmentId?: string; // Make optional for UI compatibility
  createdAt?: string;
  updatedAt?: string;
}

export interface Department {
  id: string;
  name: string;
  createdAt?: string;
  updatedAt?: string;
  workActivities?: WorkActivity[];
  expanded?: boolean; // UI state, not from API
}

/**
 * Fetch all departments
 * @returns Promise with array of departments
 */
export const fetchDepartments = async (): Promise<Department[]> => {
  try {
    console.log('Fetching departments from API');
    const data = await api.get<Department[]>(DEPARTMENTS_ENDPOINT);

    // Add expanded property for UI state
    const departmentsWithUIState = data.map(dept => ({
      ...dept,
      expanded: false
    }));

    console.log('Departments fetched successfully:', departmentsWithUIState);
    return departmentsWithUIState;
  } catch (error) {
    console.error('Failed to fetch departments:', error);
    toast({
      title: 'Error',
      description: 'Failed to fetch departments. Please try again.',
      variant: 'destructive',
    });
    return [];
  }
};

/**
 * Create a new department
 * @param name The name of the department to create
 * @returns Promise with the created department
 */
export const createDepartment = async (name: string): Promise<Department | null> => {
  try {
    console.log('Creating new department:', name);
    const response = await api.post<Department>(DEPARTMENTS_ENDPOINT, { name });

    console.log('Department created successfully:', response);
    toast({
      title: 'Success',
      description: 'Department created successfully.',
    });

    return {
      ...response,
      expanded: false,
      workActivities: []
    };
  } catch (error) {
    console.error('Failed to create department:', error);
    toast({
      title: 'Error',
      description: 'Failed to create department. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Update a department
 * @param id The ID of the department to update
 * @param name The new name for the department
 * @returns Promise with success flag
 */
export const updateDepartment = async (id: string, name: string): Promise<boolean> => {
  try {
    console.log(`Updating department ${id} with name:`, name);
    await api.patch<any>(`${DEPARTMENTS_ENDPOINT}/${id}`, { name });

    console.log('Department updated successfully');
    toast({
      title: 'Success',
      description: 'Department updated successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to update department:', error);
    toast({
      title: 'Error',
      description: 'Failed to update department. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Delete a department
 * @param id The ID of the department to delete
 * @returns Promise with success flag
 */
export const deleteDepartment = async (id: string): Promise<boolean> => {
  try {
    console.log(`Deleting department ${id}`);
    await api.delete(`${DEPARTMENTS_ENDPOINT}/${id}`);

    console.log('Department deleted successfully');
    toast({
      title: 'Success',
      description: 'Department deleted successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to delete department:', error);
    toast({
      title: 'Error',
      description: 'Failed to delete department. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Fetch work activities for a department
 * @param departmentId The ID of the department
 * @returns Promise with array of work activities
 */
export const fetchWorkActivities = async (departmentId: string): Promise<WorkActivity[]> => {
  try {
    console.log(`Fetching work activities for department ${departmentId}`);
    const data = await api.get<WorkActivity[]>(WORK_ACTIVITIES_ENDPOINT(departmentId));

    // Ensure each activity has the departmentId
    const activitiesWithDepartmentId = data.map(activity => ({
      ...activity,
      departmentId
    }));

    console.log('Work activities fetched successfully:', activitiesWithDepartmentId);
    return activitiesWithDepartmentId;
  } catch (error) {
    console.error('Failed to fetch work activities:', error);
    toast({
      title: 'Error',
      description: 'Failed to fetch work activities. Please try again.',
      variant: 'destructive',
    });
    return [];
  }
};

/**
 * Create a new work activity
 * @param departmentId The ID of the parent department
 * @param name The name of the work activity to create
 * @returns Promise with the created work activity
 */
export const createWorkActivity = async (departmentId: string, name: string): Promise<WorkActivity | null> => {
  try {
    console.log(`Creating new work activity for department ${departmentId}:`, name);
    const response = await api.post<WorkActivity>(WORK_ACTIVITIES_ENDPOINT(departmentId), { name });

    console.log('Work activity created successfully:', response);
    toast({
      title: 'Success',
      description: 'Work activity created successfully.',
    });

    // Ensure the departmentId is included in the response
    return {
      ...response,
      departmentId
    };
  } catch (error) {
    console.error('Failed to create work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to create work activity. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Update a work activity
 * @param departmentId The ID of the parent department
 * @param activityId The ID of the work activity to update
 * @param name The new name for the work activity
 * @returns Promise with success flag
 */
export const updateWorkActivity = async (departmentId: string, activityId: string, name: string): Promise<boolean> => {
  try {
    console.log(`Updating work activity ${activityId} with name:`, name);
    await api.patch<any>(`${WORK_ACTIVITIES_ENDPOINT(departmentId)}/${activityId}`, { name });

    console.log('Work activity updated successfully');
    toast({
      title: 'Success',
      description: 'Work activity updated successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to update work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to update work activity. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Delete a work activity
 * @param departmentId The ID of the parent department
 * @param activityId The ID of the work activity to delete
 * @returns Promise with success flag
 */
export const deleteWorkActivity = async (departmentId: string, activityId: string): Promise<boolean> => {
  try {
    console.log(`Deleting work activity ${activityId}`);
    await api.delete(`${WORK_ACTIVITIES_ENDPOINT(departmentId)}/${activityId}`);

    console.log('Work activity deleted successfully');
    toast({
      title: 'Success',
      description: 'Work activity deleted successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to delete work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to delete work activity. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};
