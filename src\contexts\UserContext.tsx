import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
// Import the script to clear localStorage
import '../clearLocalStorage';

// Define the User interface
export interface User {
  id: string;
  name: string;
  email: string;
  company: string;
  status: 'active' | 'inactive';
  blocked?: boolean;
  dateAdded?: string; // Date when user was added
  lastActive?: string; // Date when user was last active
}

// Define the context interface
interface UserContextType {
  users: User[];
  addUser: (user: Omit<User, 'id'>) => void;
  updateUser: (id: string, userData: Partial<User>) => void;
  deleteUser: (id: string) => void;
  toggleUserStatus: (id: string, active: boolean) => void;
  toggleUserBlocked: (id: string, blocked: boolean) => void;
}

// Create the context with a default value
const UserContext = createContext<UserContextType | undefined>(undefined);

// Provider props interface
interface UserProviderProps {
  children: ReactNode;
}

// Create a provider component
export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [users, setUsers] = useState<User[]>([]);

  // Load users from localStorage on initial render
  useEffect(() => {
    const storedUsers = localStorage.getItem('users');
    if (storedUsers) {
      try {
        setUsers(JSON.parse(storedUsers));
      } catch (error) {
        console.error('Failed to parse stored users:', error);
      }
    } else {
      // Add sample users if no users exist in localStorage
      const now = new Date();
      const formattedDate = now.toISOString();
      const twoWeeksAgo = new Date(now);
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
      const twoWeeksAgoFormatted = twoWeeksAgo.toISOString();

      const oneMonthAgo = new Date(now);
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
      const oneMonthAgoFormatted = oneMonthAgo.toISOString();

      const sampleUsers: User[] = [
        {
          id: '1',
          name: 'Adhi',
          email: '<EMAIL>',
          company: 'AcuiZen',
          status: 'active',
          blocked: false,
          dateAdded: oneMonthAgoFormatted,
          lastActive: formattedDate
        },
        {
          id: '2',
          name: 'surendhar',
          email: '<EMAIL>',
          company: 'AcuiZen',
          status: 'active',
          blocked: false,
          dateAdded: twoWeeksAgoFormatted,
          lastActive: twoWeeksAgoFormatted
        },
        {
          id: '3',
          name: 'John External',
          email: '<EMAIL>',
          company: 'External Corp',
          status: 'active',
          blocked: false,
          dateAdded: twoWeeksAgoFormatted,
          lastActive: formattedDate
        },
        {
          id: '4',
          name: 'Alice Contract',
          email: '<EMAIL>',
          company: 'Contract LLC',
          status: 'inactive',
          blocked: false,
          dateAdded: oneMonthAgoFormatted,
          lastActive: oneMonthAgoFormatted
        }
      ];

      setUsers(sampleUsers);
    }
  }, []);

  // Save users to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('users', JSON.stringify(users));
  }, [users]);

  // Add a new user
  const addUser = (userData: Omit<User, 'id'>) => {
    const now = new Date();
    const formattedDate = now.toISOString();

    const newUser: User = {
      ...userData,
      id: Date.now().toString(), // Generate a unique ID
      dateAdded: formattedDate,
      lastActive: formattedDate, // Initially, last active is same as date added
    };
    setUsers(prevUsers => [...prevUsers, newUser]);
  };

  // Update an existing user
  const updateUser = (id: string, userData: Partial<User>) => {
    const now = new Date();
    const formattedDate = now.toISOString();

    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === id ? {
          ...user,
          ...userData,
          lastActive: formattedDate // Update last active timestamp
        } : user
      )
    );
  };

  // Delete a user
  const deleteUser = (id: string) => {
    setUsers(prevUsers => prevUsers.filter(user => user.id !== id));
  };

  // Toggle user active status
  const toggleUserStatus = (id: string, active: boolean) => {
    const now = new Date();
    const formattedDate = now.toISOString();

    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === id ? {
          ...user,
          status: active ? 'active' : 'inactive',
          lastActive: formattedDate // Update last active timestamp
        } : user
      )
    );
  };

  // Toggle user blocked status
  const toggleUserBlocked = (id: string, blocked: boolean) => {
    const now = new Date();
    const formattedDate = now.toISOString();

    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === id ? {
          ...user,
          blocked,
          lastActive: formattedDate // Update last active timestamp
        } : user
      )
    );
  };

  // Create the context value object
  const contextValue: UserContextType = {
    users,
    addUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    toggleUserBlocked,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the user context
export const useUsers = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUsers must be used within a UserProvider');
  }
  return context;
};
