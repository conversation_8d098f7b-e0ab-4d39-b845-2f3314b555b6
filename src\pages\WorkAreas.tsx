import { useState, useRef, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Check,
  X,
  Loader2
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchWork<PERSON>reas,
  createWork<PERSON>rea,
  updateWorkArea,
  deleteWorkArea
} from "@/store/slices/workAreaSlice";
import { WorkArea } from "@/services/workAreaService";

const WorkAreas = () => {
  const dispatch = useAppDispatch();
  const { workAreas, isLoading, error } = useAppSelector((state) => state.workAreas);
  const [searchTerm, setSearchTerm] = useState("");

  // State for editing
  const [editingWorkAreaId, setEditingWorkAreaId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>("");
  const editInputRef = useRef<HTMLInputElement>(null);

  // State for dialogs
  const [addWorkAreaDialogOpen, setAddWorkAreaDialogOpen] = useState(false);
  const [newWorkAreaName, setNewWorkAreaName] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [workAreaToDelete, setWorkAreaToDelete] = useState<WorkArea | null>(null);

  // Fetch work areas on component mount
  useEffect(() => {
    dispatch(fetchWorkAreas());
  }, [dispatch]);

  // Work Area editing functions
  const handleStartEditWorkArea = (workAreaId: string, name: string) => {
    setEditingWorkAreaId(workAreaId);
    setEditValue(name);

    // Focus the input field after it's rendered
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
      }
    }, 10);
  };

  const handleSaveEditWorkArea = () => {
    if (!editingWorkAreaId || !editValue.trim()) {
      setEditingWorkAreaId(null);
      setEditValue("");
      return;
    }

    // Dispatch update action
    dispatch(updateWorkArea({ id: editingWorkAreaId, name: editValue.trim() }));

    setEditingWorkAreaId(null);
    setEditValue("");
  };

  const handleCancelEditWorkArea = () => {
    setEditingWorkAreaId(null);
    setEditValue("");
  };

  // Work Area deletion functions
  const handleConfirmDeleteWorkArea = (workArea: WorkArea) => {
    setWorkAreaToDelete(workArea);
    setDeleteDialogOpen(true);
  };

  const handleDeleteWorkArea = () => {
    if (!workAreaToDelete) return;

    // Dispatch delete action
    dispatch(deleteWorkArea(workAreaToDelete.id));

    setDeleteDialogOpen(false);
    setWorkAreaToDelete(null);
  };

  // Add new work area function
  const handleAddWorkArea = () => {
    if (!newWorkAreaName.trim()) {
      setAddWorkAreaDialogOpen(false);
      setNewWorkAreaName("");
      return;
    }

    // Dispatch create action
    dispatch(createWorkArea(newWorkAreaName.trim()));

    setAddWorkAreaDialogOpen(false);
    setNewWorkAreaName("");
  };

  const filteredWorkAreas = workAreas.filter((area) =>
    area.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title="Work Areas"
          subtitle="Manage work areas in your organization"
          actions={
            <Button onClick={() => {
              setAddWorkAreaDialogOpen(true);
              setNewWorkAreaName("");
              // Focus will be handled by the dialog's onOpenAutoFocus
            }}>
              <Plus className="mr-2 h-4 w-4" />
              Add Work Area
            </Button>
          }
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="pt-6">
                <div className="relative mb-6">
                  <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search work areas..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9"
                  />
                </div>

                {isLoading && (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2 text-muted-foreground">Loading work areas...</span>
                  </div>
                )}

                {error && (
                  <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-6">
                    <p className="font-medium">Error</p>
                    <p>{error}</p>
                  </div>
                )}


                {filteredWorkAreas.length > 0 ? (
                  <div className="space-y-2">
                    {filteredWorkAreas.map((workArea) => (
                      <div key={workArea.id} className="border rounded-md overflow-hidden">
                        <div className="flex items-center justify-between p-3 bg-muted/30">
                          <div className="flex items-center">
                            {editingWorkAreaId === workArea.id ? (
                              <div className="flex-1 flex items-center">
                                <Input
                                  ref={editInputRef}
                                  value={editValue}
                                  onChange={(e) => setEditValue(e.target.value)}
                                  className="mr-2"
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                      handleSaveEditWorkArea();
                                    } else if (e.key === 'Escape') {
                                      handleCancelEditWorkArea();
                                    }
                                  }}
                                />
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={handleSaveEditWorkArea}
                                  className="h-8 w-8 p-0 mr-1"
                                >
                                  <Check className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={handleCancelEditWorkArea}
                                  className="h-8 w-8 p-0"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <>
                                <span className="font-medium">{workArea.name}</span>
                              </>
                            )}
                          </div>
                          {editingWorkAreaId !== workArea.id && (
                            <div className="flex items-center">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleStartEditWorkArea(workArea.id, workArea.name)}
                                className="h-8 w-8 p-0 mr-1"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleConfirmDeleteWorkArea(workArea)}
                                className="h-8 w-8 p-0 text-destructive"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    {searchTerm ? (
                      <p>No work areas found matching "{searchTerm}"</p>
                    ) : (
                      <p>No work areas found. Add one to get started.</p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="lg:col-span-1">
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-semibold mb-2">About Work Areas</h3>
                <p className="text-muted-foreground mb-4">
                  Work areas represent departments or functional areas within your organization.
                </p>
                <p className="text-muted-foreground mb-4">
                  Each work area can have multiple work activities associated with it.
                </p>
                <p className="text-muted-foreground">
                  Use the "Add Work Area" button to create new work areas. You can manage work activities on the Work Activities page.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the work area "{workAreaToDelete?.name}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteWorkArea}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Add Work Area Dialog */}
      <Dialog open={addWorkAreaDialogOpen} onOpenChange={setAddWorkAreaDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Work Area</DialogTitle>
            <DialogDescription>
              Create a new work area to organize work activities.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="work-area-name" className="text-right col-span-1">
                Name
              </label>
              <div className="col-span-3">
                <Input
                  id="work-area-name"
                  value={newWorkAreaName}
                  onChange={(e) => setNewWorkAreaName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleAddWorkArea();
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button onClick={handleAddWorkArea}>Add Work Area</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
};

export default WorkAreas;
