/**
 * Storage Service
 * 
 * This service provides a centralized way to interact with browser storage (localStorage/sessionStorage).
 * It handles serialization/deserialization, expiration, and provides a consistent API.
 */

// Type for stored items with expiration
interface StoredItem<T> {
  value: T;
  expiry?: number; // Timestamp when the item expires (optional)
}

// Storage types
type StorageType = 'local' | 'session';

// Default expiration time (1 hour in milliseconds)
const DEFAULT_EXPIRY = 3600000;

/**
 * Get an item from storage
 * @param key The key to retrieve
 * @param storageType The type of storage to use (local or session)
 * @returns The stored value or null if not found or expired
 */
export function getItem<T>(key: string, storageType: StorageType = 'local'): T | null {
  try {
    // Get the storage object
    const storage = storageType === 'local' ? localStorage : sessionStorage;
    
    // Get the item from storage
    const item = storage.getItem(key);
    if (!item) return null;
    
    // Parse the stored item
    const storedItem: StoredItem<T> = JSON.parse(item);
    
    // Check if the item has expired
    if (storedItem.expiry && storedItem.expiry < Date.now()) {
      // Item has expired, remove it
      storage.removeItem(key);
      return null;
    }
    
    // Return the value
    return storedItem.value;
  } catch (error) {
    console.error(`Error getting item '${key}' from ${storageType} storage:`, error);
    return null;
  }
}

/**
 * Set an item in storage
 * @param key The key to store
 * @param value The value to store
 * @param expiryMs Expiration time in milliseconds (optional)
 * @param storageType The type of storage to use (local or session)
 */
export function setItem<T>(
  key: string, 
  value: T, 
  expiryMs?: number, 
  storageType: StorageType = 'local'
): void {
  try {
    // Get the storage object
    const storage = storageType === 'local' ? localStorage : sessionStorage;
    
    // Create the stored item
    const storedItem: StoredItem<T> = {
      value
    };
    
    // Add expiry if provided
    if (expiryMs) {
      storedItem.expiry = Date.now() + expiryMs;
    }
    
    // Store the item
    storage.setItem(key, JSON.stringify(storedItem));
  } catch (error) {
    console.error(`Error setting item '${key}' in ${storageType} storage:`, error);
  }
}

/**
 * Remove an item from storage
 * @param key The key to remove
 * @param storageType The type of storage to use (local or session)
 */
export function removeItem(key: string, storageType: StorageType = 'local'): void {
  try {
    // Get the storage object
    const storage = storageType === 'local' ? localStorage : sessionStorage;
    
    // Remove the item
    storage.removeItem(key);
  } catch (error) {
    console.error(`Error removing item '${key}' from ${storageType} storage:`, error);
  }
}

/**
 * Clear all items from storage
 * @param storageType The type of storage to clear (local or session)
 */
export function clearStorage(storageType: StorageType = 'local'): void {
  try {
    // Get the storage object
    const storage = storageType === 'local' ? localStorage : sessionStorage;
    
    // Clear the storage
    storage.clear();
  } catch (error) {
    console.error(`Error clearing ${storageType} storage:`, error);
  }
}

// Export a default object with all functions
export default {
  getItem,
  setItem,
  removeItem,
  clearStorage,
  DEFAULT_EXPIRY
};
