import React, { createContext, useContext, useState, useEffect, useRef, ReactNode } from 'react';
// AWS Amplify v6 auth functions
import { signIn, signOut } from 'aws-amplify/auth'; // For login/logout
import { getCurrentUser } from 'aws-amplify/auth'; // For getting current user
import { updatePassword } from 'aws-amplify/auth'; // For changing password
import { resetPassword, confirmResetPassword } from 'aws-amplify/auth'; // For password reset flow
import { fetchAuthSession } from 'aws-amplify/auth'; // For getting the auth session and token
import storageService from '@/services/storageService'; // For token storage
import { toast } from '@/hooks/use-toast';
import { initializeAmplify, isCognitoConfigured } from '@/lib/cognito';
import { getLoginConfig, LoginConfig, defaultConfig } from '@/services/loginConfigService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { store } from '@/store';
import {
  loginSuccess,
  logoutSuccess,
  setAuthLoading,
  setUser as setReduxUser
} from '@/store/slices/authSlice';
import { fetchUserProfile } from '@/services/userProfileService';

// Initialize Amplify with default config first
// We'll update it once we fetch the login config
initializeAmplify();

// Define the user interface for AWS Amplify v6
export interface AuthUser {
  username: string;
  userId: string;
  signInDetails?: {
    loginId: string;
  };
  // Add any other properties that might be needed
  email?: string;
  attributes?: Record<string, any>;
}

// Define the context interface
interface AuthContextType {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<AuthUser | null>;
  logout: () => Promise<void>;
  forgotPassword: (username: string) => Promise<boolean>;
  resetPassword: (username: string, code: string, newPassword: string) => Promise<boolean>;
  changePassword: (oldPassword: string, newPassword: string) => Promise<boolean>;
  handleOAuthCallback: (code: string) => Promise<AuthUser | null>; // New function to handle OAuth callback
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider props interface
interface AuthProviderProps {
  children: ReactNode;
}

// Create a provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Use Redux for auth state
  const dispatch = useAppDispatch();
  const { user, isLoading } = useAppSelector((state) => state.auth);
  const [loginConfig, setLoginConfig] = useState<LoginConfig | null>(null);

  // Fetch login configuration and initialize Amplify
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        // Fetch login configuration from API
        const config = await getLoginConfig();
        setLoginConfig(config);

        // Re-initialize Amplify with the fetched configuration
        initializeAmplify(config);
      } catch (error) {
        console.error('Failed to fetch login configuration:', error);
      }
    };

    fetchConfig();
  }, []);

  // Check if the user is authenticated on initial render
  useEffect(() => {
    const checkAuthState = async () => {
      try {
        dispatch(setAuthLoading(true));
        console.log('Checking authentication state...');

        // First, check if we have tokens in our standard localStorage keys
        const accessToken = localStorage.getItem('access_token');
        const idToken = localStorage.getItem('id_token');
        const refreshToken = localStorage.getItem('refresh_token');

        console.log('Tokens in standard localStorage keys:', {
          accessToken: accessToken ? 'Present' : 'Missing',
          idToken: idToken ? 'Present' : 'Missing',
          refreshToken: refreshToken ? 'Present' : 'Missing'
        });

        // Check for Cognito tokens in localStorage and copy them to our standard keys
        if (!accessToken) {
          console.log('No access token found in standard localStorage keys, checking Cognito localStorage keys');

          // Get all localStorage keys
          const keys = Object.keys(localStorage);
          console.log('All localStorage keys:', keys);

          // Find keys that match the Cognito pattern and contain 'accessToken'
          const accessTokenKeys = keys.filter(key =>
            key.startsWith('CognitoIdentityServiceProvider.') &&
            key.endsWith('.accessToken')
          );

          if (accessTokenKeys.length > 0) {
            console.log('Found Cognito accessToken keys:', accessTokenKeys);

            // Use the first one
            const cognitoAccessToken = localStorage.getItem(accessTokenKeys[0]);

            if (cognitoAccessToken) {
              console.log('Found Cognito access token, copying to standard localStorage keys');

              // Extract the key prefix
              const foundKeyPrefix = accessTokenKeys[0].replace('.accessToken', '');

              // Try to get the other tokens with the same prefix
              const cognitoIdToken = localStorage.getItem(`${foundKeyPrefix}.idToken`);
              const cognitoRefreshToken = localStorage.getItem(`${foundKeyPrefix}.refreshToken`);

              // Store in our standard keys
              localStorage.setItem('access_token', cognitoAccessToken);

              if (cognitoIdToken) {
                localStorage.setItem('id_token', cognitoIdToken);
              }

              if (cognitoRefreshToken) {
                localStorage.setItem('refresh_token', cognitoRefreshToken);
              }

              localStorage.setItem('token_type', 'Bearer');
              localStorage.setItem('expires_in', '3600');

              // Also store in our storage service for backward compatibility
              storageService.setItem('auth_token', cognitoAccessToken, 3600 * 1000);

              console.log('Cognito tokens copied to standard localStorage keys');
            }
          }
        }

        if (!isCognitoConfigured()) {
          console.warn('AWS Cognito is not configured properly. Authentication will not work.');
          dispatch(setAuthLoading(false));
          return;
        }

        try {
          // Try to get the current user from Cognito
          const currentUser = await getCurrentUser();
          console.log('Current user retrieved from Cognito:', currentUser);

          // Try to get the current session to extract the token
          try {
            const session = await fetchAuthSession();
            console.log('Auth session retrieved:', session);

            // Extract the token from the session
            let accessToken = '';

            if (session && typeof session === 'object') {
              const sessionObj = session as Record<string, any>;

              if ('tokens' in sessionObj && sessionObj.tokens && typeof sessionObj.tokens === 'object') {
                const tokens = sessionObj.tokens as Record<string, any>;

                if ('accessToken' in tokens && tokens.accessToken) {
                  const token = tokens.accessToken as Record<string, any>;

                  if ('toString' in token && typeof token.toString === 'function') {
                    accessToken = token.toString();

                    // Store the token in our storage service
                    storageService.setItem('auth_token', accessToken, 3600000); // 1 hour expiration

                    // Also store in localStorage with the key 'access_token' as required
                    localStorage.setItem('access_token', accessToken);
                  }
                }
              }
            }

            // If we couldn't get the token from the session, try to get it from Cognito localStorage keys
            if (!accessToken) {
              console.log('No token found in session, checking Cognito localStorage keys');

              // Try to extract tokens from Cognito localStorage keys
              const cognitoTokens = extractTokensFromCognitoStorage(currentUser.username);

              if (cognitoTokens) {
                console.log('Found tokens in Cognito localStorage keys');
                accessToken = cognitoTokens.access_token;

                // Store all tokens in localStorage
                localStorage.setItem('access_token', cognitoTokens.access_token);
                localStorage.setItem('id_token', cognitoTokens.id_token);
                localStorage.setItem('refresh_token', cognitoTokens.refresh_token);
                localStorage.setItem('token_type', cognitoTokens.token_type);
                localStorage.setItem('expires_in', String(cognitoTokens.expires_in));

                // Also store the token in our storage service for backward compatibility
                storageService.setItem('auth_token', cognitoTokens.access_token, cognitoTokens.expires_in * 1000);
              }
            }

            // Update Redux state with the authenticated user and token
            dispatch(setReduxUser(currentUser));
            dispatch(loginSuccess({
              user: currentUser as any,
              token: accessToken || 'placeholder-token-for-development'
            }));
            console.log('Redux state updated with authenticated user and token');
          } catch (sessionError) {
            console.error('Error getting auth session:', sessionError);

            // Update Redux state with just the user if we couldn't get the token
            dispatch(setReduxUser(currentUser));
            dispatch(loginSuccess({
              user: currentUser as any,
              token: 'placeholder-token-for-development'
            }));
            console.log('Redux state updated with authenticated user (no token)');
          }
        } catch (error) {
          console.log('No authenticated user found in Cognito');
          // Check if we have a user in Redux already
          const reduxState = store.getState();
          const reduxUser = reduxState.auth.user;

          if (reduxUser) {
            console.log('Using persisted user from Redux:', reduxUser);
            // Keep the Redux user
          } else {
            console.log('No authenticated user found in Redux either');
            // User is not authenticated
            dispatch(setReduxUser(null));
          }
        }
      } catch (error) {
        console.error('Error checking auth state:', error);
        // User is not authenticated
        dispatch(setReduxUser(null));
      } finally {
        dispatch(setAuthLoading(false));
      }
    };

    checkAuthState();
  }, [loginConfig, dispatch]); // Re-check auth state when login config changes

  // Function to extract tokens from Cognito localStorage keys
  const extractTokensFromCognitoStorage = (username: string): {
    access_token: string;
    id_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
  } | null => {
    try {
      console.log('Extracting tokens from Cognito localStorage keys');

      // Initialize token variables
      let accessToken = '';
      let idToken = '';
      let refreshToken = '';
      let expiresIn = 3600; // Default 1 hour expiration
      let tokenType = 'Bearer';

      // Get the Cognito client ID from the login config
      const clientId = loginConfig?.COGNITO_ADMIN_APP_CLIENT_ID || '';
      if (!clientId) {
        console.error('No Cognito client ID available');
        return null;
      }

      // Format the username for Cognito keys
      // Replace special characters in the username
      const formattedUsername = username.replace(/[@.]/g, '_');
      console.log('Formatted username for Cognito keys:', formattedUsername);

      // Construct the Cognito key prefixes
      const keyPrefix = `CognitoIdentityServiceProvider.${clientId}.${formattedUsername}`;
      console.log('Cognito key prefix:', keyPrefix);

      // Look for tokens in localStorage with Cognito-specific keys
      accessToken = localStorage.getItem(`${keyPrefix}.accessToken`) || '';
      idToken = localStorage.getItem(`${keyPrefix}.idToken`) || '';
      refreshToken = localStorage.getItem(`${keyPrefix}.refreshToken`) || '';

      // Also try the 'LastAuthUser' pattern
      const lastAuthUser = localStorage.getItem(`CognitoIdentityServiceProvider.${clientId}.LastAuthUser`);
      if (lastAuthUser && !accessToken) {
        console.log('Found LastAuthUser:', lastAuthUser);
        const lastAuthUserPrefix = `CognitoIdentityServiceProvider.${clientId}.${lastAuthUser}`;
        accessToken = localStorage.getItem(`${lastAuthUserPrefix}.accessToken`) || '';
        idToken = localStorage.getItem(`${lastAuthUserPrefix}.idToken`) || '';
        refreshToken = localStorage.getItem(`${lastAuthUserPrefix}.refreshToken`) || '';
      }

      // Try to find any Cognito tokens in localStorage as a last resort
      if (!accessToken) {
        console.log('Searching for any Cognito tokens in localStorage');
        // Get all localStorage keys
        const keys = Object.keys(localStorage);
        // Find keys that match the Cognito pattern and contain 'accessToken'
        const accessTokenKeys = keys.filter(key =>
          key.startsWith('CognitoIdentityServiceProvider.') &&
          key.endsWith('.accessToken')
        );

        if (accessTokenKeys.length > 0) {
          console.log('Found Cognito accessToken keys:', accessTokenKeys);
          // Use the first one
          accessToken = localStorage.getItem(accessTokenKeys[0]) || '';

          // Extract the key prefix
          const foundKeyPrefix = accessTokenKeys[0].replace('.accessToken', '');
          // Try to get the other tokens with the same prefix
          idToken = localStorage.getItem(`${foundKeyPrefix}.idToken`) || '';
          refreshToken = localStorage.getItem(`${foundKeyPrefix}.refreshToken`) || '';
        }
      }

      if (!accessToken) {
        console.warn('No access token found in Cognito localStorage keys');
        return null;
      }

      // Log each token separately for debugging
      console.log('Access Token:', accessToken ? 'Present (length: ' + accessToken.length + ')' : 'Missing');
      console.log('ID Token:', idToken ? 'Present (length: ' + idToken.length + ')' : 'Missing');
      console.log('Refresh Token:', refreshToken ? 'Present (length: ' + refreshToken.length + ')' : 'Missing');

      return {
        access_token: accessToken,
        id_token: idToken,
        refresh_token: refreshToken,
        expires_in: expiresIn,
        token_type: tokenType
      };
    } catch (error) {
      console.error('Error extracting tokens from Cognito localStorage:', error);
      return null;
    }
  };

  // Function to extract tokens from Cognito sign-in result
  const extractTokensFromSignInResult = (signInResult: any, username: string): {
    access_token: string;
    id_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
  } | null => {
    try {
      console.log('Extracting tokens from Cognito sign-in result');

      console.log('Sign in result:', signInResult);

      // Check if the sign-in was successful
      if (signInResult &&
          typeof signInResult === 'object' &&
          'isSignedIn' in signInResult &&
          signInResult.isSignedIn === true) {

        console.log('Sign-in was successful, extracting tokens from Cognito localStorage keys');

        // Extract tokens from Cognito localStorage keys
        return extractTokensFromCognitoStorage(username);
      }

      console.warn('Sign-in result does not indicate successful sign-in');
      return null;
    } catch (error) {
      console.error('Error extracting tokens from sign-in result:', error);
      return null;
    }
  };

  // Login function
  const login = async (username: string, password: string): Promise<AuthUser | null> => {
    try {
      console.log('Starting login process for user:', username);

      // Sign in with username and password
      const signInResult = await signIn({ username, password });
      console.log('Sign in successful, result:', signInResult);

      // Log OAuth2 token endpoint information after successful login
      if (loginConfig) {
        console.log('=== OAuth2 Token Endpoint Information ===');
        console.log(`OAuth2 Token Endpoint: ${loginConfig.COGNITO_ADMIN_DOMAIN}/oauth2/token`);
        console.log('Required parameters for token request:');
        console.log('- grant_type: "authorization_code" or "refresh_token"');
        console.log(`- client_id: ${loginConfig.COGNITO_ADMIN_APP_CLIENT_ID}`);
        console.log(`- redirect_uri: ${window.location.origin}`);
        console.log('- code: [Authorization Code] (for grant_type=authorization_code)');
        console.log('- refresh_token: [Refresh Token] (for grant_type=refresh_token)');
        console.log('=== End OAuth2 Token Endpoint Information ===');
      }

      // Extract tokens from the sign-in result
      const tokens = extractTokensFromSignInResult(signInResult, username);

      if (tokens) {
        console.log('Successfully extracted tokens from sign-in result');

        // Store the token in our storage service for backward compatibility
        console.log('Storing access_token in storageService');
        storageService.setItem('auth_token', tokens.access_token, tokens.expires_in * 1000);

        // We'll rely on Redux for token storage
        console.log('Tokens will be stored in Redux state');

        // Get the current authenticated user
        const currentUser = await getCurrentUser();
        console.log('Current user retrieved:', currentUser);

        // Update Redux state with the authenticated user and token
        dispatch(loginSuccess({
          user: currentUser as any,
          token: tokens.access_token
        }));
        console.log('Redux state updated with authenticated user and token');

        // Fetch user profile from API
        console.log('Fetching user profile from API');
        await fetchUserProfile();
        console.log('User profile fetched and stored in Redux');

        // Return the user
        return currentUser as AuthUser;
      } else {
        console.warn('No tokens found in sign in result, using a placeholder token');
        // Use a placeholder token for development if no token is found
        // In production, you would want to throw an error here
        const accessToken = 'placeholder-token-for-development';

        console.log('Storing placeholder token in storage service');

        // Store the token in our storage service for backward compatibility
        storageService.setItem('auth_token', accessToken, 3600 * 1000);

        // We'll rely on Redux for token storage
        console.log('Placeholder token will be stored in Redux state');

        // Get the current authenticated user
        const currentUser = await getCurrentUser();
        console.log('Current user retrieved:', currentUser);

        // Update Redux state with the authenticated user and token
        dispatch(loginSuccess({
          user: currentUser as any,
          token: accessToken
        }));
        console.log('Redux state updated with authenticated user and token');

        // Fetch user profile from API
        console.log('Fetching user profile from API');
        await fetchUserProfile();
        console.log('User profile fetched and stored in Redux');

        // Return the user
        return currentUser as AuthUser;
      }
    } catch (error: any) {
      console.error('Login error:', error);
      const errorMessage = error.message || 'Failed to sign in';
      toast({
        title: 'Login Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      return null;
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      dispatch(setAuthLoading(true));
      await signOut();

      // Clear all tokens from storage
      storageService.removeItem('auth_token');

      // Remove all tokens from localStorage
      localStorage.removeItem('access_token');
      localStorage.removeItem('id_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('token_type');
      localStorage.removeItem('expires_in');

      // Also clear Cognito tokens from localStorage
      if (loginConfig?.COGNITO_ADMIN_APP_CLIENT_ID) {
        console.log('Clearing Cognito tokens from localStorage');

        // Get all localStorage keys
        const keys = Object.keys(localStorage);

        // Find and remove all Cognito-related keys
        keys.forEach(key => {
          if (key.startsWith('CognitoIdentityServiceProvider.')) {
            console.log('Removing Cognito key:', key);
            localStorage.removeItem(key);
          }
        });
      }

      // Update Redux state
      dispatch(logoutSuccess());

      // Navigation will be handled by the component that calls this function
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to sign out';
      toast({
        title: 'Logout Failed',
        description: errorMessage,
        variant: 'destructive',
      });

      // Even if sign out fails, clear all tokens
      storageService.removeItem('auth_token');

      // Remove all tokens from localStorage
      localStorage.removeItem('access_token');
      localStorage.removeItem('id_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('token_type');
      localStorage.removeItem('expires_in');

      // Also clear Cognito tokens from localStorage
      if (loginConfig?.COGNITO_ADMIN_APP_CLIENT_ID) {
        console.log('Clearing Cognito tokens from localStorage even though sign out failed');

        // Get all localStorage keys
        const keys = Object.keys(localStorage);

        // Find and remove all Cognito-related keys
        keys.forEach(key => {
          if (key.startsWith('CognitoIdentityServiceProvider.')) {
            console.log('Removing Cognito key:', key);
            localStorage.removeItem(key);
          }
        });
      }
    } finally {
      dispatch(setAuthLoading(false));
    }
  };

  // Forgot password function
  const forgotPassword = async (username: string): Promise<boolean> => {
    try {
      dispatch(setAuthLoading(true));
      // Use resetPassword instead of forgotPassword
      await resetPassword({ username });
      toast({
        title: 'Reset Code Sent',
        description: 'Check your email for the password reset code',
      });
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to send reset code';
      toast({
        title: 'Reset Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      return false;
    } finally {
      dispatch(setAuthLoading(false));
    }
  };

  // Reset password function - renamed to confirmPasswordReset to avoid naming conflict
  const confirmPasswordReset = async (username: string, code: string, newPassword: string): Promise<boolean> => {
    try {
      dispatch(setAuthLoading(true));
      // Use confirmResetPassword instead of forgotPasswordSubmit
      await confirmResetPassword({ username, confirmationCode: code, newPassword });
      toast({
        title: 'Password Reset',
        description: 'Your password has been reset successfully',
      });
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to reset password';
      toast({
        title: 'Reset Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      return false;
    } finally {
      dispatch(setAuthLoading(false));
    }
  };

  // Change password function
  const changePassword = async (oldPassword: string, newPassword: string): Promise<boolean> => {
    try {
      dispatch(setAuthLoading(true));
      // Get current user first to ensure we're authenticated
      await getCurrentUser();
      // Use updatePassword instead of changePassword
      await updatePassword({ oldPassword, newPassword });
      toast({
        title: 'Password Changed',
        description: 'Your password has been changed successfully',
      });
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to change password';
      toast({
        title: 'Change Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      return false;
    } finally {
      dispatch(setAuthLoading(false));
    }
  };

  // Track processed codes to prevent duplicate API calls
  const processedCodes = useRef<Set<string>>(new Set());

  // Handle OAuth callback function
  const handleOAuthCallback = async (code: string): Promise<AuthUser | null> => {
    try {
      // Check if we've already processed this code
      if (processedCodes.current.has(code)) {
        console.log('Code already processed, skipping token exchange:', code);
        return null;
      }

      // Add the code to our processed set
      processedCodes.current.add(code);

      dispatch(setAuthLoading(true));
      console.log('Handling OAuth callback with code:', code);

      // If login configuration is not available, use the default configuration
      const config = loginConfig || defaultConfig;

      if (!config.COGNITO_ADMIN_DOMAIN || !config.COGNITO_ADMIN_APP_CLIENT_ID) {
        console.error('Cognito configuration is incomplete');
        toast({
          title: 'Authentication Error',
          description: 'Cognito configuration is incomplete. Please check your configuration.',
          variant: 'destructive'
        });
        return null;
      }

      console.log('Using Cognito configuration:', {
        domain: config.COGNITO_ADMIN_DOMAIN,
        clientId: config.COGNITO_ADMIN_APP_CLIENT_ID,
        region: config.COGNITO_REGION
      });

      // Exchange the authorization code for tokens using the token endpoint
      console.log('Exchanging authorization code for tokens');

      try {
        // Prepare the token request parameters
        const tokenEndpoint = `${config.COGNITO_ADMIN_DOMAIN}/oauth2/token`;

        // IMPORTANT: This redirect URI must be EXACTLY the same as the one used in the authorization request
        // Including trailing slashes, protocol (http vs https), and any path components
        const redirectUri = window.location.origin + '/login';
        const clientId = config.COGNITO_ADMIN_APP_CLIENT_ID;

        console.log('Token endpoint:', tokenEndpoint);
        console.log('Redirect URI:', redirectUri);
        console.log('Client ID:', clientId);

        // Create form data for the token request
        const formData = new URLSearchParams();
        formData.append('grant_type', 'authorization_code');
        formData.append('client_id', clientId);
        formData.append('code', code);
        formData.append('redirect_uri', redirectUri);

        // Log the complete request for debugging
        console.log('Token request payload:', formData.toString());

        // Make the token request with a timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        let response;
        try {
          response = await fetch(tokenEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData.toString(),
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            let errorData;
            try {
              errorData = await response.json();
            } catch (e) {
              errorData = await response.text();
            }

            console.error('Token exchange failed:', response.status, errorData);

            // Check for specific error types
            if (typeof errorData === 'object' && errorData.error === 'invalid_grant') {
              throw new Error('The authorization code is invalid or has expired. Please try logging in again.');
            }

            throw new Error(`Token exchange failed: ${response.status} ${JSON.stringify(errorData)}`);
          }
        } catch (fetchError) {
          clearTimeout(timeoutId);
          throw fetchError;
        }

        // Parse the token response
        const tokenData = await response.json();
        console.log('Token exchange successful, received tokens');

        if (tokenData.access_token) {
          // Store tokens in localStorage
          localStorage.setItem('access_token', tokenData.access_token);

          if (tokenData.id_token) {
            localStorage.setItem('id_token', tokenData.id_token);
          }

          if (tokenData.refresh_token) {
            localStorage.setItem('refresh_token', tokenData.refresh_token);
          }

          localStorage.setItem('token_type', tokenData.token_type || 'Bearer');
          localStorage.setItem('expires_in', String(tokenData.expires_in || 3600));

          // Store the token in our storage service for backward compatibility
          console.log('OAuth Callback: Storing access_token in storageService');
          storageService.setItem('auth_token', tokenData.access_token, (tokenData.expires_in || 3600) * 1000);

          try {
            // Get the current authenticated user
            const currentUser = await getCurrentUser();
            console.log('Current user retrieved:', currentUser);

            // Update Redux state with the authenticated user and token
            dispatch(loginSuccess({
              user: currentUser as any,
              token: tokenData.access_token
            }));
            console.log('Redux state updated with authenticated user and token');

            // Fetch user profile from API
            console.log('Fetching user profile from API');
            await fetchUserProfile();
            console.log('User profile fetched and stored in Redux');

            // Return the user
            return currentUser as AuthUser;
          } catch (userError) {
            console.error('Error getting current user:', userError);

            // Even if we can't get the current user, we have the tokens, so create a placeholder user
            const placeholderUser = {
              username: 'cognito-user',
              userId: 'cognito-user-id',
              email: '<EMAIL>'
            };

            // Update Redux state with the placeholder user and token
            dispatch(loginSuccess({
              user: placeholderUser as any,
              token: tokenData.access_token
            }));
            console.log('Redux state updated with placeholder user and token');

            // Fetch user profile from API
            console.log('Fetching user profile from API');
            await fetchUserProfile();
            console.log('User profile fetched and stored in Redux');

            return placeholderUser as AuthUser;
          }
        } else {
          console.error('No access token in token response');
          throw new Error('No access token in token response');
        }
      } catch (error) {
        console.error('Error during OAuth token exchange:', error);
        throw error;
      }
    } catch (error: any) {
      console.error('OAuth callback error:', error);
      const errorMessage = error.message || 'Failed to complete authentication';
      toast({
        title: 'Authentication Error',
        description: errorMessage,
        variant: 'destructive',
      });
      return null;
    } finally {
      dispatch(setAuthLoading(false));
    }
  };

  // Create the context value object
  const contextValue: AuthContextType = {
    user: user as AuthUser,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    forgotPassword,
    resetPassword: confirmPasswordReset, // Use the renamed function
    changePassword,
    handleOAuthCallback, // Add the new function
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
