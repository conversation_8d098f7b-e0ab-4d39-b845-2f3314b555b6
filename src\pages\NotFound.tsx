
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

const NotFound = () => {
  const location = useLocation();
  const { t } = useTranslation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <MainLayout>
      <div className="content-container flex flex-col items-center justify-center min-h-[80vh]">
        <h1 className="text-6xl font-bold text-gray-800 mb-4">404</h1>
        <p className="text-xl text-muted-foreground mb-8">{t("errors.pageNotFound")}</p>
        <Button asChild>
          <Link to="/">{t("navigation.home")}</Link>
        </Button>
      </div>
    </MainLayout>
  );
};

export default NotFound;
