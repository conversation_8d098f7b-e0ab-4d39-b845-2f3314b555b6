import { fetchUserProfile } from '@/services/userProfileService';

/**
 * Helper function to fetch user profile
 * This can be called from the browser console for testing
 */
export const getUserProfile = async (): Promise<void> => {
  try {
    console.log('Fetching user profile from API');
    const profile = await fetchUserProfile();
    console.log('User profile:', profile);
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
  }
};

// Make the function available in the global window object for testing
if (typeof window !== 'undefined') {
  (window as any).getUserProfile = getUserProfile;
}
