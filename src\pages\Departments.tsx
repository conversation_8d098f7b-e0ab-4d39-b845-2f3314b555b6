
import { useState, useRef, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Plus,
  ChevronDown,
  ChevronRight,
  Edit,
  Trash2,
  Search,
  Check,
  X,
  Loader2
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  fetchWorkActivities,
  createWorkActivity,
  updateWorkActivity,
  deleteWorkActivity,
  toggleDepartmentExpanded
} from "@/store/slices/departmentSlice";
import { Department, WorkActivity } from "@/services/departmentService";

const Departments = () => {
  const dispatch = useAppDispatch();
  const { departments, isLoading, error } = useAppSelector((state) => state.departments);
  const [searchTerm, setSearchTerm] = useState("");

  // State for editing
  const [editingDepartmentId, setEditingDepartmentId] = useState<string | null>(null);
  const [editingActivityId, setEditingActivityId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>("");
  const editInputRef = useRef<HTMLInputElement>(null);

  // State for deletion
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [departmentToDelete, setDepartmentToDelete] = useState<Department | null>(null);
  const [activityToDelete, setActivityToDelete] = useState<{activity: WorkActivity, departmentId: string} | null>(null);

  // State for adding new activity
  const [addingActivityToDepartmentId, setAddingActivityToDepartmentId] = useState<string | null>(null);
  const [newActivityName, setNewActivityName] = useState<string>("");

  // State for adding new department
  const [addDepartmentDialogOpen, setAddDepartmentDialogOpen] = useState<boolean>(false);
  const [newDepartmentName, setNewDepartmentName] = useState<string>("");

  // Fetch departments on component mount
  useEffect(() => {
    dispatch(fetchDepartments());
  }, [dispatch]);

  const toggleDepartment = (departmentId: string) => {
    // Find the department first to check its current expanded state
    const department = departments.find(dept => dept.id === departmentId);
    if (!department) return;

    // If we're expanding and there are no work activities, fetch them first
    if (!department.expanded) {
      // Fetch work activities if they don't exist or are empty
      if (!department.workActivities || department.workActivities.length === 0) {
        dispatch(fetchWorkActivities(departmentId));
      }
    }

    // Toggle the expanded state in Redux
    dispatch(toggleDepartmentExpanded(departmentId));
  };

  // Department editing functions
  const handleStartEditDepartment = (departmentId: string, name: string) => {
    setEditingDepartmentId(departmentId);
    setEditValue(name);

    // Focus the input field after it's rendered
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
      }
    }, 10);
  };

  const handleSaveEditDepartment = () => {
    if (!editingDepartmentId || !editValue.trim()) {
      setEditingDepartmentId(null);
      setEditValue("");
      return;
    }

    // Dispatch update action
    dispatch(updateDepartment({ id: editingDepartmentId, name: editValue.trim() }));

    setEditingDepartmentId(null);
    setEditValue("");
  };

  const handleCancelEditDepartment = () => {
    setEditingDepartmentId(null);
    setEditValue("");
  };

  // Activity editing functions
  const handleStartEditActivity = (_departmentId: string, activityId: string, name: string) => {
    // Using _departmentId to indicate it's intentionally unused in this function
    setEditingActivityId(activityId);
    setEditValue(name);

    // Focus the input field after it's rendered
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
      }
    }, 10);
  };

  const handleSaveEditActivity = (departmentId: string) => {
    if (!editingActivityId || !editValue.trim()) {
      setEditingActivityId(null);
      setEditValue("");
      return;
    }

    // Dispatch update action
    dispatch(updateWorkActivity({
      departmentId,
      activityId: editingActivityId,
      name: editValue.trim()
    }));

    setEditingActivityId(null);
    setEditValue("");
  };

  const handleCancelEditActivity = () => {
    setEditingActivityId(null);
    setEditValue("");
  };

  // Department deletion functions
  const handleInitiateDeleteDepartment = (department: Department) => {
    setDepartmentToDelete(department);
    setDeleteDialogOpen(true);
  };

  const handleDeleteDepartment = () => {
    if (!departmentToDelete) return;

    // Dispatch delete action
    dispatch(deleteDepartment(departmentToDelete.id));

    setDeleteDialogOpen(false);
    setDepartmentToDelete(null);
  };

  // Activity deletion functions
  const handleInitiateDeleteActivity = (departmentId: string, activity: WorkActivity) => {
    setActivityToDelete({ activity, departmentId });
    setDeleteDialogOpen(true);
  };

  const handleDeleteActivity = () => {
    if (!activityToDelete) return;

    // Dispatch delete action
    dispatch(deleteWorkActivity({
      departmentId: activityToDelete.departmentId,
      activityId: activityToDelete.activity.id
    }));

    setDeleteDialogOpen(false);
    setActivityToDelete(null);
  };

  // Add new department function
  const handleAddDepartment = () => {
    if (!newDepartmentName.trim()) {
      setAddDepartmentDialogOpen(false);
      setNewDepartmentName("");
      return;
    }

    // Dispatch create action
    dispatch(createDepartment(newDepartmentName.trim()));

    setAddDepartmentDialogOpen(false);
    setNewDepartmentName("");
  };

  // Add new activity function
  const handleAddActivity = (departmentId: string) => {
    if (!newActivityName.trim()) {
      setAddingActivityToDepartmentId(null);
      setNewActivityName("");
      return;
    }

    // Dispatch create action
    dispatch(createWorkActivity({
      departmentId,
      name: newActivityName.trim()
    }));

    setAddingActivityToDepartmentId(null);
    setNewActivityName("");
  };

  // Handle key press in edit mode
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, type: 'department' | 'activity', departmentId?: string) => {
    e.stopPropagation();
    if (e.key === 'Enter') {
      if (type === 'department') {
        handleSaveEditDepartment();
      } else if (type === 'activity' && departmentId) {
        handleSaveEditActivity(departmentId);
      }
    } else if (e.key === 'Escape') {
      if (type === 'department') {
        handleCancelEditDepartment();
      } else if (type === 'activity') {
        handleCancelEditActivity();
      }
    }
  };

  // Handle key press in add mode
  const handleAddKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, type: 'department' | 'activity', departmentId?: string) => {
    if (e.key === 'Enter') {
      if (type === 'department') {
        handleAddDepartment();
      } else if (type === 'activity' && departmentId) {
        handleAddActivity(departmentId);
      }
    } else if (e.key === 'Escape') {
      if (type === 'department') {
        // Modal will handle its own closing with Escape
      } else if (type === 'activity') {
        setAddingActivityToDepartmentId(null);
        setNewActivityName("");
      }
    }
  };

  const filteredDepartments = departments.filter((dept) =>
    dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (dept.workActivities && dept.workActivities.some((activity) =>
      activity.name.toLowerCase().includes(searchTerm.toLowerCase())
    ))
  );

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title="Departments & Work Activities"
          subtitle="Manage departments and their associated work activities"
          actions={
            <Button onClick={() => {
              setAddDepartmentDialogOpen(true);
              setNewDepartmentName("");
              // Focus will be handled by the dialog's onOpenAutoFocus
            }}>
              <Plus className="mr-2 h-4 w-4" />
              Add Department
            </Button>
          }
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="pt-6">
                <div className="relative mb-6">
                  <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search departments and activities..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9"
                  />
                </div>

                {isLoading && (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2 text-muted-foreground">Loading departments...</span>
                  </div>
                )}

                {error && (
                  <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-6">
                    <p className="font-medium">Error</p>
                    <p>{error}</p>
                  </div>
                )}


                {filteredDepartments.length > 0 ? (
                  <div className="space-y-2">
                    {filteredDepartments.map((department) => (
                      <div key={department.id} className="border rounded-md overflow-hidden">
                        <div
                          className={cn(
                            "flex items-center justify-between p-3 bg-muted/30",
                            editingDepartmentId !== department.id && "cursor-pointer hover:bg-muted/50"
                          )}
                          onClick={() => {
                            if (editingDepartmentId !== department.id) {
                              toggleDepartment(department.id);
                            }
                          }}
                        >
                          <div className="flex items-center flex-1">
                            {department.expanded ?
                              <ChevronDown className="h-4 w-4 mr-2" /> :
                              <ChevronRight className="h-4 w-4 mr-2" />
                            }

                            {editingDepartmentId === department.id ? (
                              <Input
                                ref={editInputRef}
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                onKeyDown={(e) => handleKeyDown(e, 'department')}
                                className="h-7 text-sm py-1"
                                onClick={(e) => e.stopPropagation()}
                              />
                            ) : (
                              <span className="font-medium">{department.name}</span>
                            )}
                          </div>

                          {editingDepartmentId === department.id ? (
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleSaveEditDepartment();
                                }}
                                className="text-green-600 hover:text-green-700 hover:bg-green-50"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleCancelEditDepartment();
                                }}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleStartEditDepartment(department.id, department.name);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleInitiateDeleteDepartment(department);
                                }}
                                className="text-destructive hover:bg-destructive/10"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>

                        {department.expanded && (
                          <div className="p-3 border-t">
                            <div className="flex justify-between items-center mb-4">
                              <h4 className="text-sm font-medium text-muted-foreground">Work Activities</h4>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setAddingActivityToDepartmentId(department.id);
                                  // Focus the input field after it's rendered
                                  setTimeout(() => {
                                    const addActivityInput = document.getElementById(`add-activity-input-${department.id}`);
                                    if (addActivityInput) {
                                      (addActivityInput as HTMLInputElement).focus();
                                    }
                                  }, 10);
                                }}
                              >
                                <Plus className="h-3 w-3 mr-1" />
                                Add Activity
                              </Button>
                            </div>

                            <ul className="divide-y">
                              {/* Loading indicator for work activities */}
                              {isLoading && department.workActivities && department.workActivities.length === 0 && (
                                <li className="py-4 flex justify-center items-center">
                                  <Loader2 className="h-5 w-5 animate-spin text-primary mr-2" />
                                  <span className="text-sm text-muted-foreground">Loading work activities...</span>
                                </li>
                              )}

                              {/* Add Activity Input */}
                              {addingActivityToDepartmentId === department.id && (
                                <li className="py-2 px-1 flex items-center justify-between">
                                  <Input
                                    id={`add-activity-input-${department.id}`}
                                    value={newActivityName}
                                    onChange={(e) => setNewActivityName(e.target.value)}
                                    onKeyDown={(e) => handleAddKeyDown(e, 'activity', department.id)}
                                    placeholder="Enter activity name..."
                                    className="h-7 text-sm py-1 flex-1 mr-2"
                                    autoFocus
                                  />
                                  <div className="flex items-center gap-1">
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => handleAddActivity(department.id)}
                                      className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                    >
                                      <Check className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => {
                                        setAddingActivityToDepartmentId(null);
                                        setNewActivityName("");
                                      }}
                                    >
                                      <X className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </li>
                              )}

                              {/* No work activities message */}
                              {department.workActivities && department.workActivities.length === 0 && !isLoading && (
                                <li className="py-4 text-center text-muted-foreground">
                                  No work activities found. Add one using the button above.
                                </li>
                              )}

                              {department.workActivities && department.workActivities.map((activity) => (
                                <li
                                  key={activity.id}
                                  className="py-2 px-1 flex items-center justify-between hover:bg-muted/30 rounded-sm"
                                >
                                  {editingActivityId === activity.id ? (
                                    <Input
                                      ref={editInputRef}
                                      value={editValue}
                                      onChange={(e) => setEditValue(e.target.value)}
                                      onKeyDown={(e) => handleKeyDown(e, 'activity', department.id)}
                                      className="h-7 text-sm py-1 flex-1 mr-2"
                                    />
                                  ) : (
                                    <span>{activity.name}</span>
                                  )}

                                  {editingActivityId === activity.id ? (
                                    <div className="flex items-center gap-1">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleSaveEditActivity(department.id)}
                                        className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                      >
                                        <Check className="h-3 w-3" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={handleCancelEditActivity}
                                      >
                                        <X className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  ) : (
                                    <div className="flex items-center gap-1">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleStartEditActivity(department.id, activity.id, activity.name)}
                                      >
                                        <Edit className="h-3 w-3" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleInitiateDeleteActivity(department.id, activity)}
                                        className="text-destructive hover:bg-destructive/10"
                                      >
                                        <Trash2 className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  )}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No departments or work activities found. Try a different search term.
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardContent className="pt-6">
              <h3 className="text-lg font-medium mb-4">Quick Help</h3>
              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-medium">Departments</h4>
                  <p className="text-muted-foreground">
                    Departments represent functional areas within your organization. Each department can have multiple associated work activities.
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Work Activities</h4>
                  <p className="text-muted-foreground">
                    Work activities are specific tasks or functions performed within a department. They can be used throughout the platform to categorize and filter data.
                  </p>
                </div>

                <div className="p-3 bg-muted/30 rounded-md">
                  <h4 className="font-medium">Tip</h4>
                  <p className="text-muted-foreground">
                    Click on a department to expand and see its work activities. Use the buttons on the right to edit or delete items.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {departmentToDelete
                ? "Are you sure you want to delete this department?"
                : "Are you sure you want to delete this work activity?"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {departmentToDelete ? (
                <>
                  You are about to delete <strong>{departmentToDelete.name}</strong> department.
                  {departmentToDelete.workActivities && departmentToDelete.workActivities.length > 0 && (
                    <> This will also delete all {departmentToDelete.workActivities.length} work activities under it.</>
                  )}
                </>
              ) : activityToDelete ? (
                <>
                  You are about to delete <strong>{activityToDelete.activity.name}</strong> work activity.
                </>
              ) : null}
              <br /><br />
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setDepartmentToDelete(null);
              setActivityToDelete(null);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (departmentToDelete) {
                  handleDeleteDepartment();
                } else if (activityToDelete) {
                  handleDeleteActivity();
                }
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Add Department Dialog */}
      <Dialog open={addDepartmentDialogOpen} onOpenChange={setAddDepartmentDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Department</DialogTitle>
            <DialogDescription>
              Create a new department to organize work activities.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="department-name" className="text-right col-span-1">
                Name
              </label>
              <div className="col-span-3">
                <Input
                  id="department-name"
                  value={newDepartmentName}
                  onChange={(e) => setNewDepartmentName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleAddDepartment();
                    }
                  }}
                  placeholder="Enter department name"
                  className="w-full"
                  autoFocus
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              onClick={handleAddDepartment}
              disabled={!newDepartmentName.trim()}
            >
              Add Department
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
};

export default Departments;
