import { useState, useEffect } from "react";
import { useU<PERSON>s, User as ContextUser } from "@/contexts/UserContext";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import {
  Card,
  CardContent
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";
import {
  Search,
  UserPlus,
  Pencil,
  KeyRound,
  Fingerprint,
  Ban,
  Trash2,
  Check,
  X,
  Loader2
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { usePagination } from "@/hooks/usePagination";
import TablePagination from "@/components/common/TablePagination";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchUsers,
  createUserWithSpecificFormat,
  updateUser,
  updateUserWithSpecificUrl,
  deleteUser,
  resetUserMFA
} from "@/store/slices/userSlice";
import { User as ApiUser } from "@/services/userService";

// Use the User interface from our context but adapt it for the component
interface User extends ContextUser {
  // The context uses string IDs, but this component uses number IDs
  id: number;
  type?: 'Internal' | 'External';
}

// Form validation schema for adding a new user
const addUserSchema = z.object({
  firstName: z.string().min(3, "Name must be at least 3 characters").max(50, "Name must be less than 50 characters"),
  email: z.string().email("Please enter a valid email address"),
  company: z.string().min(1, "Please select a company"),
  type: z.enum(["Internal", "External"], {
    required_error: "Please select a user type",
  })
});

// Form data type for adding a new user
type AddUserFormData = z.infer<typeof addUserSchema>;

export default function UserManagement() {
  const dispatch = useAppDispatch();
  const { internalUsers, externalUsers, isLoading, error } = useAppSelector((state) => state.users);

  const [searchTerm, setSearchTerm] = useState('');
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [authResetDialogOpen, setAuthResetDialogOpen] = useState(false);
  const [userToResetAuth, setUserToResetAuth] = useState<ApiUser | null>(null);

  // Filter users based on search term
  const filteredInternalUsers = internalUsers.filter(user =>
    (user.firstName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (user.email?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (user.company?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  const filteredExternalUsers = externalUsers.filter(user =>
    (user.firstName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (user.email?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (user.company?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  // No longer using the context

  // Fetch users from API when component mounts
  useEffect(() => {
    dispatch(fetchUsers());
  }, [dispatch]);

  // Function to handle user actions
  const handleUserAction = (action: string, userId: string, userType: 'internal' | 'external') => {
    console.log(`Action: ${action}, User ID: ${userId}, Type: ${userType}`);

    // Find the user in the appropriate list
    const userList = userType === 'internal' ? internalUsers : externalUsers;
    const user = userList.find(u => u.id === userId);
    if (!user) return;

    switch (action) {
      case 'edit':
        // In a real app, you would open an edit dialog
        console.log('Editing user:', user);
        break;

      case 'delete':
        // Dispatch delete action to Redux
        dispatch(deleteUser(userId));
        break;

      case 'activate':
      case 'deactivate':
        // Use the specific URL for the user with ID 194a35fc-a0d1-7060-4bbb-e1f5fff6068c
        if (userId === '194a35fc-a0d1-7060-4bbb-e1f5fff6068c') {
          dispatch(updateUserWithSpecificUrl({
            id: userId,
            userData: { status: action === 'activate' ? 'active' : 'inactive' }
          }));
        } else {
          // Dispatch regular update action to Redux for other users
          dispatch(updateUser({
            id: userId,
            userData: { status: action === 'activate' ? 'active' : 'inactive' }
          }));
        }
        break;

      case 'block':
      case 'unblock':
        // Use the specific URL for the user with ID 194a35fc-a0d1-7060-4bbb-e1f5fff6068c
        if (userId === '194a35fc-a0d1-7060-4bbb-e1f5fff6068c') {
          dispatch(updateUserWithSpecificUrl({
            id: userId,
            userData: { blocked: action === 'block' }
          }));
        } else {
          // Dispatch regular update action to Redux for other users
          dispatch(updateUser({
            id: userId,
            userData: { blocked: action === 'block' }
          }));
        }
        break;

      // Reset password functionality would be implemented in a real app
      case 'reset-password':
        console.log(`Reset password functionality would be implemented in a real app`);
        break;

      // Authentication reset functionality
      case 'auth-reset':
        // Open the confirmation dialog
        setUserToResetAuth(user);
        setAuthResetDialogOpen(true);
        break;
    }
  };

  // State to track which user is being edited
  const [editingUser, setEditingUser] = useState<{ id: string, field: string } | null>(null);
  const [editValue, setEditValue] = useState<string>("");

  // Function to handle inline edit
  const handleInlineEdit = (userId: string, field: string, value: string) => {
    setEditingUser({ id: userId, field });
    setEditValue(value);
  };

  // Function to save inline edit
  const saveInlineEdit = (userId: string, field: string, userType: 'internal' | 'external') => {
    // Basic validation
    if (field === 'firstName' && editValue.trim().length < 3) {
      alert('Name must be at least 3 characters');
      return;
    }

    if (field === 'email' && !editValue.includes('@')) {
      alert('Please enter a valid email address');
      return;
    }

    if (field === 'company' && editValue.trim().length === 0) {
      alert('Company name cannot be empty');
      return;
    }

    // Find the user in the appropriate list
    const userList = userType === 'internal' ? internalUsers : externalUsers;
    const user = userList.find(u => u.id === userId);
    if (!user) return;

    // Create update data
    const updateData: Partial<ApiUser> = {
      [field]: editValue
    };

    // Use the specific URL for the user with ID 194a35fc-a0d1-7060-4bbb-e1f5fff6068c
    if (userId === '194a35fc-a0d1-7060-4bbb-e1f5fff6068c') {
      // Dispatch update action with specific URL to Redux
      dispatch(updateUserWithSpecificUrl({
        id: userId,
        userData: updateData
      }));
    } else {
      // Dispatch regular update action to Redux for other users
      dispatch(updateUser({
        id: userId,
        userData: updateData
      }));
    }

    setEditingUser(null);
  };

  // Function to cancel inline edit
  const cancelInlineEdit = () => {
    setEditingUser(null);
  };

  // Function to handle authentication reset
  const handleAuthReset = () => {
    if (userToResetAuth && userToResetAuth.email) {
      dispatch(resetUserMFA(userToResetAuth.email));
      setAuthResetDialogOpen(false);
      setUserToResetAuth(null);
    }
  };

  const UserTable = ({ users, userType }: { users: ApiUser[], userType: 'internal' | 'external' }) => {
    const { paginatedData, currentPage, totalPages, goToPage } = usePagination({
      data: users,
      itemsPerPage: 10
    });

    return (
      <div>
        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow>
              <TableHead className="font-semibold text-gray-700">Name</TableHead>
              <TableHead className="font-semibold text-gray-700">Email</TableHead>
              <TableHead className="font-semibold text-gray-700">Company</TableHead>
              <TableHead className="font-semibold text-gray-700">Date Added</TableHead>
              <TableHead className="font-semibold text-gray-700">Last Active</TableHead>
              <TableHead className="font-semibold text-gray-700">Status</TableHead>
              <TableHead className="w-[220px] text-center font-semibold text-gray-700">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex justify-center items-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
                    <span className="text-muted-foreground">Loading users...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : users.length > 0 ? (
              paginatedData.map((user) => (
                <TableRow
                  key={user.id}
                  className={user.blocked ? "bg-red-50/10" : ""}
                >
                  <TableCell className="font-medium">
                    {editingUser?.id === user.id && editingUser?.field === 'firstName' ? (
                      <div className="flex items-center space-x-2">
                        <Input
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="h-8 py-1"
                          autoFocus
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              saveInlineEdit(user.id!, 'firstName', userType);
                            } else if (e.key === 'Escape') {
                              cancelInlineEdit();
                            }
                          }}
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => saveInlineEdit(user.id!, 'firstName', userType)}
                          className="h-8 w-8 text-green-600"
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={cancelInlineEdit}
                          className="h-8 w-8 text-red-600"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div
                        className="flex items-center group cursor-pointer"
                        onClick={() => handleInlineEdit(user.id!, 'firstName', user.firstName)}
                      >
                        <span>{user.firstName}</span>
                        <Pencil className="h-3.5 w-3.5 ml-2 opacity-0 group-hover:opacity-100 text-muted-foreground" />
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {editingUser?.id === user.id && editingUser?.field === 'email' ? (
                      <div className="flex items-center space-x-2">
                        <Input
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="h-8 py-1"
                          autoFocus
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              saveInlineEdit(user.id!, 'email', userType);
                            } else if (e.key === 'Escape') {
                              cancelInlineEdit();
                            }
                          }}
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => saveInlineEdit(user.id!, 'email', userType)}
                          className="h-8 w-8 text-green-600"
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={cancelInlineEdit}
                          className="h-8 w-8 text-red-600"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div
                        className="flex items-center group cursor-pointer"
                        onClick={() => handleInlineEdit(user.id!, 'email', user.email)}
                      >
                        <span>{user.email}</span>
                        <Pencil className="h-3.5 w-3.5 ml-2 opacity-0 group-hover:opacity-100 text-muted-foreground" />
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {editingUser?.id === user.id && editingUser?.field === 'company' ? (
                      <div className="flex items-center space-x-2">
                        <Input
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="h-8 py-1"
                          autoFocus
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              saveInlineEdit(user.id!, 'company', userType);
                            } else if (e.key === 'Escape') {
                              cancelInlineEdit();
                            }
                          }}
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => saveInlineEdit(user.id!, 'company', userType)}
                          className="h-8 w-8 text-green-600"
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={cancelInlineEdit}
                          className="h-8 w-8 text-red-600"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div
                        className="flex items-center group cursor-pointer"
                        onClick={() => handleInlineEdit(user.id!, 'company', user.company)}
                      >
                        <span>{user.company}</span>
                        <Pencil className="h-3.5 w-3.5 ml-2 opacity-0 group-hover:opacity-100 text-muted-foreground" />
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {user.dateAdded ? format(new Date(user.dateAdded), 'MMM dd, yyyy') :
                     user.created ? format(new Date(user.created), 'MMM dd, yyyy') : '-'}
                  </TableCell>
                  <TableCell>
                    {user.lastActive ? format(new Date(user.lastActive), 'MMM dd, yyyy') : '-'}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={user.status === "active" ? "default" : "outline"}
                      className={user.status === "active" ? "bg-green-500 hover:bg-green-600 text-white rounded-full px-3" : ""}
                    >
                      {user.status === "active" ? "Active" : "Inactive"}
                    </Badge>
                    {user.blocked && (
                      <Badge variant="outline" className="ml-2 bg-red-100 text-red-800 border-red-200">
                        Blocked
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center justify-center space-x-1">
                      <TooltipProvider>
                        {/* Edit User */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleInlineEdit(user.id!, 'firstName', user.firstName)}
                              className="h-8 w-8 text-gray-500 hover:text-blue-600"
                            >
                              <Pencil className="h-4 w-4" />
                              <span className="sr-only">Edit user</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Edit User</p>
                          </TooltipContent>
                        </Tooltip>

                        {/* Reset Password */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleUserAction('reset-password', user.id!, userType)}
                              className="h-8 w-8 text-gray-500 hover:text-blue-600"
                            >
                              <KeyRound className="h-4 w-4" />
                              <span className="sr-only">Reset password</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Reset Password</p>
                          </TooltipContent>
                        </Tooltip>

                        {/* Authentication Reset */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleUserAction('auth-reset', user.id!, userType)}
                              className="h-8 w-8 text-gray-500 hover:text-blue-600"
                            >
                              <Fingerprint className="h-4 w-4" />
                              <span className="sr-only">Authentication reset</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Authentication Reset</p>
                          </TooltipContent>
                        </Tooltip>

                        {/* Activate/Deactivate User */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center justify-center h-8 w-8">
                              <Switch
                                checked={user.status === "active"}
                                onCheckedChange={(checked) =>
                                  handleUserAction(checked ? 'activate' : 'deactivate', user.id!, userType)
                                }
                                className="data-[state=checked]:bg-green-500"
                              />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{user.status === "active" ? "Deactivate" : "Activate"} User</p>
                          </TooltipContent>
                        </Tooltip>

                        {/* Block/Unblock User */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className={`h-8 w-8 ${user.blocked ? "text-red-500" : "text-gray-500 hover:text-blue-600"}`}
                              onClick={() => handleUserAction(user.blocked ? 'unblock' : 'block', user.id!, userType)}
                            >
                              <Ban className="h-4 w-4" />
                              <span className="sr-only">{user.blocked ? "Unblock" : "Block"} user</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{user.blocked ? "Unblock" : "Block"} User</p>
                          </TooltipContent>
                        </Tooltip>

                        {/* Delete User */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-red-500 hover:text-red-600"
                              onClick={() => handleUserAction('delete', user.id!, userType)}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete user</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Delete User</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  {error ? (
                    <div className="text-destructive">
                      Error loading users: {error}
                    </div>
                  ) : (
                    "No users found. Try adding a new user."
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        {users.length > 0 && (
          <div className="py-4 px-2 border-t">
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
            />
          </div>
        )}
      </div>
    );
  };

  // Add User form with validation
  const addUserForm = useForm<AddUserFormData>({
    resolver: zodResolver(addUserSchema),
    defaultValues: {
      firstName: "",
      email: "",
      company: "",
      type: "Internal"
    }
  });

  // No longer tracking selected tab as it's handled by the Tabs component

  const handleAddUser = (data: AddUserFormData) => {
    console.log("Adding new user:", data);

    // Create new user object with the specified format
    const newUser: ApiUser = {
      firstName: data.firstName,
      email: data.email,
      company: data.company,
      type: data.type
    };

    // Use the specific format for creating users
    dispatch(createUserWithSpecificFormat(newUser));

    // Close the dialog and reset the form
    setAddUserDialogOpen(false);
    addUserForm.reset();
  };

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title="Users Management"
          subtitle="Manage user accounts and their details"
        />

        <Card className="border-t-0 rounded-t-none shadow-sm">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
              <div className="relative w-full md:w-auto">
                <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 w-full md:w-[300px]"
                />
              </div>

              <Button onClick={() => setAddUserDialogOpen(true)} className="bg-blue-600 hover:bg-blue-700">
                <UserPlus className="mr-2 h-4 w-4" />
                Create User
              </Button>
            </div>

            <Tabs
              defaultValue="internal"
              className="w-full"
            >
              <TabsList className="mb-6 bg-gray-100 p-1 rounded-md">
                <TabsTrigger value="internal" className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">Internal Users</TabsTrigger>
                <TabsTrigger value="external" className="rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">External Users</TabsTrigger>
              </TabsList>
              <TabsContent value="internal">
                <div className="border rounded-md overflow-hidden">
                  <UserTable users={filteredInternalUsers} userType="internal" />
                </div>
              </TabsContent>
              <TabsContent value="external">
                <div className="border rounded-md overflow-hidden">
                  <UserTable users={filteredExternalUsers} userType="external" />
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Authentication Reset Dialog */}
      <AlertDialog open={authResetDialogOpen} onOpenChange={setAuthResetDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reset Authentication</AlertDialogTitle>
            <AlertDialogDescription>
              {userToResetAuth && (
                <>
                  Are you sure you want to reset authentication for <strong>{userToResetAuth.email}</strong>?
                  <br /><br />
                  This will reset the user's multi-factor authentication.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setUserToResetAuth(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleAuthReset}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Reset Authentication
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Add User Dialog */}
      <Dialog open={addUserDialogOpen} onOpenChange={setAddUserDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create User</DialogTitle>
            <DialogDescription>
              Create a new user account. Fill in all required fields.
            </DialogDescription>
          </DialogHeader>

          <Form {...addUserForm}>
            <form onSubmit={addUserForm.handleSubmit(handleAddUser)} className="space-y-4 py-4">
              <FormField
                control={addUserForm.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={addUserForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email ID</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={addUserForm.control}
                name="company"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter company name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={addUserForm.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>User Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select user type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Internal">Internal</SelectItem>
                        <SelectItem value="External">External</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setAddUserDialogOpen(false);
                    addUserForm.reset();
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">Add User</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
