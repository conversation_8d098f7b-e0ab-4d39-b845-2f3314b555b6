import { useState } from "react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, FileText, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { usePagination } from "@/hooks/usePagination";
import TablePagination from "@/components/common/TablePagination";

// Form validation schema
const changeRequestSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters").max(100, "Title must be less than 100 characters"),
  description: z.string().min(10, "Description must be at least 10 characters").max(500, "Description must be less than 500 characters"),
  reason: z.string().min(5, "Reason must be at least 5 characters").max(300, "Reason must be less than 300 characters"),
  requestedBy: z.string().min(2, "Requested by field is required"),
  changeOwner: z.string().min(2, "Change owner field is required"),
});

type ChangeRequestFormData = z.infer<typeof changeRequestSchema>;

interface ChangeRequestItem {
  id: string;
  title: string;
  description: string;
  reason: string;
  requestedBy: string;
  changeOwner: string;
  dateRequested: string;
  reviewStatus: "Pending" | "In Review" | "Approved" | "Rejected";
  approvalStatus: "Pending" | "Approved" | "Denied";
  implementationStatus: "Pending" | "In Progress" | "Completed" | "Failed";
  documentationStatus: "Pending" | "In Progress" | "Completed";
}

const ChangeRequest = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Sample data for change requests
  const changeRequests: ChangeRequestItem[] = [
    {
      id: "CR-001",
      title: "Update User Authentication System",
      description: "Implement multi-factor authentication for enhanced security",
      reason: "Security compliance requirements",
      requestedBy: "John Smith",
      changeOwner: "IT Security Team",
      dateRequested: "2023-08-28",
      reviewStatus: "Approved",
      approvalStatus: "Approved",
      implementationStatus: "In Progress",
      documentationStatus: "Pending"
    },
    {
      id: "CR-002",
      title: "Database Performance Optimization",
      description: "Optimize database queries and indexing for better performance",
      reason: "System performance degradation reported",
      requestedBy: "Sarah Williams",
      changeOwner: "Database Team",
      dateRequested: "2023-08-27",
      reviewStatus: "In Review",
      approvalStatus: "Pending",
      implementationStatus: "Pending",
      documentationStatus: "Pending"
    },
    {
      id: "CR-003",
      title: "Add New Reporting Module",
      description: "Develop comprehensive reporting dashboard for management",
      reason: "Business requirement for better insights",
      requestedBy: "Robert Williams",
      changeOwner: "Development Team",
      dateRequested: "2023-08-26",
      reviewStatus: "Approved",
      approvalStatus: "Approved",
      implementationStatus: "Completed",
      documentationStatus: "Completed"
    }
  ];

  // Form setup
  const form = useForm<ChangeRequestFormData>({
    resolver: zodResolver(changeRequestSchema),
    defaultValues: {
      title: "",
      description: "",
      reason: "",
      requestedBy: "",
      changeOwner: "",
    },
  });

  const onSubmit = (data: ChangeRequestFormData) => {
    console.log("Change request submitted:", data);
    // Here you would typically send the data to your backend
    form.reset();
  };

  // Filter change requests
  const filteredRequests = changeRequests.filter((request) => {
    const matchesSearch = 
      request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.requestedBy.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || 
      request.reviewStatus.toLowerCase() === statusFilter.toLowerCase() ||
      request.approvalStatus.toLowerCase() === statusFilter.toLowerCase() ||
      request.implementationStatus.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return <Badge variant="secondary" className="flex items-center gap-1"><Clock className="h-3 w-3" />{status}</Badge>;
      case "in review":
      case "in progress":
        return <Badge variant="default" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" />{status}</Badge>;
      case "approved":
      case "completed":
        return <Badge variant="default" className="bg-green-500 flex items-center gap-1"><CheckCircle className="h-3 w-3" />{status}</Badge>;
      case "rejected":
      case "denied":
      case "failed":
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" />{status}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title={t("changeRequest.title")}
          subtitle={t("changeRequest.subtitle")}
        />

        {/* Submission Form */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              {t("changeRequest.submitNew")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("changeRequest.title")}</FormLabel>
                        <FormControl>
                          <Input placeholder={t("changeRequest.titlePlaceholder")} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="requestedBy"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("changeRequest.requestedBy")}</FormLabel>
                        <FormControl>
                          <Input placeholder={t("changeRequest.requestedByPlaceholder")} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("changeRequest.description")}</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder={t("changeRequest.descriptionPlaceholder")} 
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("changeRequest.reason")}</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder={t("changeRequest.reasonPlaceholder")} 
                          className="min-h-[80px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="changeOwner"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("changeRequest.changeOwner")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("changeRequest.changeOwnerPlaceholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end">
                  <Button type="submit" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    {t("changeRequest.submit")}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Change Requests Table */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("changeRequest.searchPlaceholder")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>

              <div className="w-full md:w-[200px]">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder={t("changeRequest.filterByStatus")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t("common.all")} {t("common.status")}</SelectItem>
                    <SelectItem value="pending">{t("changeRequest.pending")}</SelectItem>
                    <SelectItem value="in review">{t("changeRequest.inReview")}</SelectItem>
                    <SelectItem value="approved">{t("changeRequest.approved")}</SelectItem>
                    <SelectItem value="completed">{t("changeRequest.completed")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="rounded-md border">
              {(() => {
                const { paginatedData, currentPage, totalPages, goToPage } = usePagination({
                  data: filteredRequests,
                  itemsPerPage: 10
                });

                return (
                  <>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t("changeRequest.id")}</TableHead>
                          <TableHead>{t("changeRequest.title")}</TableHead>
                          <TableHead>{t("changeRequest.requestedBy")}</TableHead>
                          <TableHead>{t("changeRequest.dateRequested")}</TableHead>
                          <TableHead>{t("changeRequest.review")}</TableHead>
                          <TableHead>{t("changeRequest.approval")}</TableHead>
                          <TableHead>{t("changeRequest.implementation")}</TableHead>
                          <TableHead>{t("changeRequest.documentation")}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {paginatedData.length > 0 ? (
                          paginatedData.map((request) => (
                            <TableRow key={request.id}>
                              <TableCell className="font-mono text-sm">{request.id}</TableCell>
                              <TableCell className="max-w-xs">
                                <div>
                                  <div className="font-medium truncate">{request.title}</div>
                                  <div className="text-sm text-muted-foreground truncate">{request.description}</div>
                                </div>
                              </TableCell>
                              <TableCell>{request.requestedBy}</TableCell>
                              <TableCell>{format(new Date(request.dateRequested), "MMM dd, yyyy")}</TableCell>
                              <TableCell>{getStatusBadge(request.reviewStatus)}</TableCell>
                              <TableCell>{getStatusBadge(request.approvalStatus)}</TableCell>
                              <TableCell>{getStatusBadge(request.implementationStatus)}</TableCell>
                              <TableCell>{getStatusBadge(request.documentationStatus)}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                              {t("changeRequest.noRequestsFound")}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                    {filteredRequests.length > 0 && (
                      <TablePagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={goToPage}
                      />
                    )}
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default ChangeRequest;
