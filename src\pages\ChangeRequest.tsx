import { useState } from "react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, FileText, Clock, CheckCircle, XCircle, AlertCircle, X, Eye, Download, File, Image } from "lucide-react";
import { usePagination } from "@/hooks/usePagination";
import TablePagination from "@/components/common/TablePagination";

// Form validation schema
const changeRequestSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters").max(100, "Title must be less than 100 characters"),
  description: z.string().min(10, "Description must be at least 10 characters").max(500, "Description must be less than 500 characters"),
  reason: z.string().min(5, "Reason must be at least 5 characters").max(300, "Reason must be less than 300 characters"),
  requestedBy: z.string().min(2, "Requested by field is required"),
  changeOwner: z.string().min(2, "Change owner field is required"),
  urgency: z.enum(["low", "medium", "high", "critical"], {
    required_error: "Please select urgency level",
  }),
  requiredDate: z.string().min(1, "Required date is required"),
  attachments: z.any().optional(),
});

type ChangeRequestFormData = z.infer<typeof changeRequestSchema>;

interface ChangeRequestItem {
  id: string;
  title: string;
  description: string;
  reason: string;
  requestedBy: string;
  changeOwner: string;
  dateRequested: string;
  reviewStatus: "Pending" | "In Review" | "Approved" | "Rejected";
  approvalStatus: "Pending" | "Approved" | "Denied";
  implementationStatus: "Pending" | "In Progress" | "Completed" | "Failed";
  documentationStatus: "Pending" | "In Progress" | "Completed";
}

const ChangeRequest = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);

  // Helper functions for file handling
  const handleFileSelect = (files: FileList | null) => {
    if (files) {
      const newFiles = Array.from(files);
      setAttachedFiles(prev => [...prev, ...newFiles]);
    }
  };

  const removeFile = (index: number) => {
    setAttachedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="h-4 w-4" />;
    }
    return <File className="h-4 w-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const previewFile = (file: File) => {
    if (file.type.startsWith('image/')) {
      const url = URL.createObjectURL(file);
      window.open(url, '_blank');
    } else {
      // For non-image files, we could implement a document viewer
      // For now, just show file info
      alert(`File: ${file.name}\nSize: ${formatFileSize(file.size)}\nType: ${file.type}`);
    }
  };

  // Sample data for change requests
  const changeRequests: ChangeRequestItem[] = [
    {
      id: "CR-001",
      title: "Update User Authentication System",
      description: "Implement multi-factor authentication for enhanced security",
      reason: "Security compliance requirements",
      requestedBy: "John Smith",
      changeOwner: "IT Security Team",
      dateRequested: "2023-08-28",
      reviewStatus: "Approved",
      approvalStatus: "Approved",
      implementationStatus: "In Progress",
      documentationStatus: "Pending"
    },
    {
      id: "CR-002",
      title: "Database Performance Optimization",
      description: "Optimize database queries and indexing for better performance",
      reason: "System performance degradation reported",
      requestedBy: "Sarah Williams",
      changeOwner: "Database Team",
      dateRequested: "2023-08-27",
      reviewStatus: "In Review",
      approvalStatus: "Pending",
      implementationStatus: "Pending",
      documentationStatus: "Pending"
    },
    {
      id: "CR-003",
      title: "Add New Reporting Module",
      description: "Develop comprehensive reporting dashboard for management",
      reason: "Business requirement for better insights",
      requestedBy: "Robert Williams",
      changeOwner: "Development Team",
      dateRequested: "2023-08-26",
      reviewStatus: "Approved",
      approvalStatus: "Approved",
      implementationStatus: "Completed",
      documentationStatus: "Completed"
    }
  ];

  // Form setup
  const form = useForm<ChangeRequestFormData>({
    resolver: zodResolver(changeRequestSchema),
    defaultValues: {
      title: "",
      description: "",
      reason: "",
      requestedBy: "",
      changeOwner: "",
      urgency: undefined,
      requiredDate: "",
      attachments: [],
    },
  });

  const onSubmit = (data: ChangeRequestFormData) => {
    const formData = {
      ...data,
      attachments: attachedFiles
    };
    console.log("Change request submitted:", formData);
    // Here you would typically send the data to your backend
    form.reset();
    setAttachedFiles([]);
    setIsDialogOpen(false);
  };

  // Filter change requests
  const filteredRequests = changeRequests.filter((request) => {
    const matchesSearch = 
      request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.requestedBy.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || 
      request.reviewStatus.toLowerCase() === statusFilter.toLowerCase() ||
      request.approvalStatus.toLowerCase() === statusFilter.toLowerCase() ||
      request.implementationStatus.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return <Badge variant="secondary" className="flex items-center gap-1"><Clock className="h-3 w-3" />{status}</Badge>;
      case "in review":
      case "in progress":
        return <Badge variant="default" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" />{status}</Badge>;
      case "approved":
      case "completed":
        return <Badge variant="default" className="bg-green-500 flex items-center gap-1"><CheckCircle className="h-3 w-3" />{status}</Badge>;
      case "rejected":
      case "denied":
      case "failed":
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" />{status}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <MainLayout>
      <div className="content-container">
        <div className="flex justify-between items-start mb-6">
          <PageHeader
            title="Change Request"
            subtitle={t("changeRequest.subtitle")}
          />

          {/* New Change Request Button */}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                {t("changeRequest.submitNew")}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {t("changeRequest.submitNew")}
                </DialogTitle>
                <DialogDescription>
                  {t("changeRequest.subtitle")}
                </DialogDescription>
              </DialogHeader>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("changeRequest.title")}</FormLabel>
                          <FormControl>
                            <Input placeholder={t("changeRequest.titlePlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="requestedBy"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("changeRequest.requestedBy")}</FormLabel>
                          <FormControl>
                            <Input placeholder={t("changeRequest.requestedByPlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("changeRequest.description")}</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder={t("changeRequest.descriptionPlaceholder")}
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="reason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("changeRequest.reason")}</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder={t("changeRequest.reasonPlaceholder")}
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="changeOwner"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("changeRequest.changeOwner")}</FormLabel>
                        <FormControl>
                          <Input placeholder={t("changeRequest.changeOwnerPlaceholder")} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="urgency"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("changeRequest.urgency")}</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={t("changeRequest.urgencyPlaceholder")} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="low">{t("changeRequest.urgencyLow")}</SelectItem>
                              <SelectItem value="medium">{t("changeRequest.urgencyMedium")}</SelectItem>
                              <SelectItem value="high">{t("changeRequest.urgencyHigh")}</SelectItem>
                              <SelectItem value="critical">{t("changeRequest.urgencyCritical")}</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="requiredDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("changeRequest.requiredDate")}</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              min={new Date().toISOString().split('T')[0]}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="attachments"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("changeRequest.attachments")}</FormLabel>
                        <FormControl>
                          <div className="space-y-4">
                            <Input
                              type="file"
                              multiple
                              accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
                              onChange={(e) => handleFileSelect(e.target.files)}
                              className="cursor-pointer"
                            />

                            {/* File Preview List */}
                            {attachedFiles.length > 0 && (
                              <div className="space-y-2">
                                <p className="text-sm font-medium">
                                  {t("changeRequest.attachedFiles")} ({attachedFiles.length})
                                </p>
                                <div className="max-h-32 overflow-y-auto space-y-2">
                                  {attachedFiles.map((file, index) => (
                                    <div
                                      key={index}
                                      className="flex items-center justify-between p-2 bg-muted rounded-md"
                                    >
                                      <div className="flex items-center gap-2 flex-1 min-w-0">
                                        {getFileIcon(file)}
                                        <div className="flex-1 min-w-0">
                                          <p className="text-sm font-medium truncate">
                                            {file.name}
                                          </p>
                                          <p className="text-xs text-muted-foreground">
                                            {formatFileSize(file.size)}
                                          </p>
                                        </div>
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <Button
                                          type="button"
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => previewFile(file)}
                                          className="h-8 w-8 p-0"
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          type="button"
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => removeFile(index)}
                                          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                        >
                                          <X className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <p className="text-sm text-muted-foreground">
                          {t("changeRequest.attachmentsHelp")}
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-end gap-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      {t("common.cancel")}
                    </Button>
                    <Button type="submit" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      {t("changeRequest.submit")}
                    </Button>
                  </div>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Change Requests Table */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("changeRequest.searchPlaceholder")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>

              <div className="w-full md:w-[200px]">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder={t("changeRequest.filterByStatus")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t("common.all")} {t("common.status")}</SelectItem>
                    <SelectItem value="pending">{t("changeRequest.pending")}</SelectItem>
                    <SelectItem value="in review">{t("changeRequest.inReview")}</SelectItem>
                    <SelectItem value="approved">{t("changeRequest.approved")}</SelectItem>
                    <SelectItem value="completed">{t("changeRequest.completed")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="rounded-md border">
              {(() => {
                const { paginatedData, currentPage, totalPages, goToPage } = usePagination({
                  data: filteredRequests,
                  itemsPerPage: 10
                });

                return (
                  <>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t("changeRequest.id")}</TableHead>
                          <TableHead>{t("changeRequest.title")}</TableHead>
                          <TableHead>{t("changeRequest.requestedBy")}</TableHead>
                          <TableHead>{t("changeRequest.dateRequested")}</TableHead>
                          <TableHead>{t("changeRequest.review")}</TableHead>
                          <TableHead>{t("changeRequest.approval")}</TableHead>
                          <TableHead>{t("changeRequest.implementation")}</TableHead>
                          <TableHead>{t("changeRequest.documentation")}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {paginatedData.length > 0 ? (
                          paginatedData.map((request) => (
                            <TableRow key={request.id}>
                              <TableCell className="font-mono text-sm">{request.id}</TableCell>
                              <TableCell className="max-w-xs">
                                <div>
                                  <div className="font-medium truncate">{request.title}</div>
                                  <div className="text-sm text-muted-foreground truncate">{request.description}</div>
                                </div>
                              </TableCell>
                              <TableCell>{request.requestedBy}</TableCell>
                              <TableCell>{format(new Date(request.dateRequested), "MMM dd, yyyy")}</TableCell>
                              <TableCell>{getStatusBadge(request.reviewStatus)}</TableCell>
                              <TableCell>{getStatusBadge(request.approvalStatus)}</TableCell>
                              <TableCell>{getStatusBadge(request.implementationStatus)}</TableCell>
                              <TableCell>{getStatusBadge(request.documentationStatus)}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                              {t("changeRequest.noRequestsFound")}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                    {filteredRequests.length > 0 && (
                      <TablePagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={goToPage}
                      />
                    )}
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default ChangeRequest;
