import { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { api } from "@/services/apiService";
import FilterLocation from "@/components/FilterLocation";

// API endpoints
const API_URL = "https://admin.client-api.acuizen.com";
const SERVICE_DETAILS = `${API_URL}/services`;
const GET_INDIVIDUAL_USER_LOCATION_ROLE_URL = `${API_URL}/user-location-roles/get-individual-users`;
const USERS_URL_WITH_ID = (id: string) => `${API_URL}/users/${id}`;
const GET_MY_USER_LOCATION_ROLE_URL = (id: string) => `${API_URL}/my-user-location-roles/${id}`;
const USER_LOCATION_ROLE_WITH_ID_URL = (id: string) => `${API_URL}/user-location-roles/${id}`;
const INDIVIDUAL_USER_LOCATION_ROLE_URL = `${API_URL}/individual-user-location-roles`;
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Label } from "@/components/ui/label";
import { Pencil, Trash2, X, RotateCcw, Loader2 } from "lucide-react";
import { usePagination } from "@/hooks/usePagination";
import TablePagination from "@/components/common/TablePagination";
import { toast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";

// Define interfaces for our data
interface User {
  id: string;
  firstName: string;
  email: string;
  company: string;
  type: 'Internal' | 'External';
  status?: boolean;
  roles?: string[];
  created?: string;
  updated?: string;
  blocked?: boolean;
}

interface Location {
  country: string;
  region: string;
  site: string;
  level: string;
  sublevel: string;
}

interface Module {
  id: string;
  name: string;
  description?: string;
  maskName?: string;
  color?: string;
  icon?: string;
  mobileShortName?: string;
  applicability?: string;
  status?: boolean;
  url?: string;
  created?: string;
  updated?: string;
  roles: Role[];
}

interface Role {
  id: string;
  name: string;
  maskName?: string;
  maskId?: string;
  created?: string;
  updated?: string;
  level?: number;
  serviceId?: string;
}

interface RoleAssignment {
  id: string;
  user: User;
  module: string;
  location: string;
  roles: string[];
  assignedDate: string;
}

interface LocationRole {
  id: string;
  userId: string;
  locationOne?: string;
  locationTwo?: string;
  locationThree?: string;
  locationFour?: string;
  locationOneId?: string;
  locationTwoId?: string;
  locationThreeId?: string;
  locationFourId?: string;
  roles: string[];
  created?: string;
  updated?: string;
  [key: string]: any;
}

export default function RoleAssignment() {
  // State for user selection
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingAssignmentId, setEditingAssignmentId] = useState<string | null>(null);
  const [editingModuleId, setEditingModuleId] = useState<string | null>(null);

  // State for reset confirmation dialog
  const [resetConfirmDialogOpen, setResetConfirmDialogOpen] = useState(false);
  const [resetAssignmentId, setResetAssignmentId] = useState<string | null>(null);

  // State for form reset confirmation dialog
  const [resetFormConfirmDialogOpen, setResetFormConfirmDialogOpen] = useState(false);

  // State for location selection
  const [selectedLocation, setSelectedLocation] = useState<Location>({
    country: "",
    region: "",
    site: "",
    level: "",
    sublevel: ""
  });

  // Handle location filter changes
  const handleLocationFilter = (tier1: string, tier2: string, tier3: string, tier4: string, tier5: string) => {
    setSelectedLocation({
      country: tier1,
      region: tier2,
      site: tier3,
      level: tier4,
      sublevel: tier5
    });
  };

  // Handle sub-level availability updates from FilterLocation component
  const handleSubLevelsAvailable = (hasSubLevels: {
    tier2Available: boolean;
    tier3Available: boolean;
    tier4Available: boolean;
    tier5Available: boolean;
  }) => {
    setSubLevelsAvailable(hasSubLevels);
  };

  // Fetch individual user roles based on location selection
  const getIndividualRoles = async () => {
    if (!selectedUser) return;

    try {
      const data = await api.post(GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, {
        userId: selectedUser,
        locations: {
          locationOne: selectedLocation.country,
          locationTwo: selectedLocation.region,
          locationThree: selectedLocation.site,
          locationFour: selectedLocation.level
        }
      });

      // Type assertion to handle the response
      const responseData = Array.isArray(data) ? data : [];

      if (responseData.length > 0) {
        setIndividualSelectedRole(responseData[0] as { roles: string[], disabledRoles: string[] });
      } else {
        setIndividualSelectedRole({ roles: [], disabledRoles: [] });
      }
    } catch (error) {
      console.error('Failed to fetch individual user roles:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch individual user roles. Please try again.',
        variant: 'destructive',
      });
      setIndividualSelectedRole({ roles: [], disabledRoles: [] });
    }
  };

  // State for selected roles
  const [selectedRoles, setSelectedRoles] = useState<Record<string, string[]>>({});

  // State for users
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);

  // State for modules
  const [modules, setModules] = useState<Module[]>([]);
  const [isLoadingModules, setIsLoadingModules] = useState(false);

  // State for individual selected roles
  const [individualSelectedRole, setIndividualSelectedRole] = useState<{
    roles: string[],
    disabledRoles: string[]
  }>({ roles: [], disabledRoles: [] });

  // State for all location roles
  const [allLocationRoles, setAllLocationRoles] = useState<LocationRole[]>([]);

  // State for custom roles from user profile
  const [customRoles, setCustomRoles] = useState<Record<string, string[]>>({});

  // State for location data arrays (matching test.js)
  const [country, setCountry] = useState<{ id: string; name: string }[]>([]);
  const [locationTwo, setLocationTwo] = useState<{ id: string; name: string }[]>([]);
  const [locationThree, setLocationThree] = useState<{ id: string; name: string }[]>([]);
  const [locationFour, setLocationFour] = useState<{ id: string; name: string }[]>([]);

  // State to track sub-level availability from FilterLocation component
  const [subLevelsAvailable, setSubLevelsAvailable] = useState({
    tier2Available: false,
    tier3Available: false,
    tier4Available: false,
    tier5Available: false
  });

  // Fetch location data (matching test.js)
  const getCountry = async () => {
    try {
      const response = await api.get('https://admin.client-api.acuizen.com/location-ones');
      setCountry(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Failed to fetch countries:', error);
      setCountry([]);
    }
  };

  const getLocationTwo = async () => {
    try {
      const response = await api.get('https://admin.client-api.acuizen.com/location-twos');
      setLocationTwo(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Failed to fetch location two:', error);
      setLocationTwo([]);
    }
  };

  const getLocationThree = async () => {
    try {
      const response = await api.get('https://admin.client-api.acuizen.com/location-threes');
      setLocationThree(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Failed to fetch location three:', error);
      setLocationThree([]);
    }
  };

  const getLocationFour = async () => {
    try {
      const response = await api.get('https://admin.client-api.acuizen.com/location-fours');
      setLocationFour(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Failed to fetch location four:', error);
      setLocationFour([]);
    }
  };

  // Fetch users from API
  useEffect(() => {
    const fetchUsers = async () => {
      setIsLoadingUsers(true);
      try {
        const response = await api.get<User[]>('https://admin.client-api.acuizen.com/users');
        // Filter only active users (status is true)
        const filteredUsers = response.filter(user =>
          user.status === true && !user.blocked
        );
        setUsers(filteredUsers);
      } catch (error) {
        console.error('Failed to fetch users:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch users. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingUsers(false);
      }
    };

    fetchUsers();
    // Also fetch location data
    getCountry();
    getLocationTwo();
    getLocationThree();
    getLocationFour();
  }, []);

  // Location data is now handled by the FilterLocation component

  // Fetch modules with roles from API
  useEffect(() => {
    const fetchModules = async () => {
      setIsLoadingModules(true);
      try {
        const params = {
          "include": [
            { "relation": "roles" }
          ]
        };

        const response = await api.get(`${SERVICE_DETAILS}?filter=${encodeURIComponent(JSON.stringify(params))}`);

        if (response && Array.isArray(response)) {
          setModules(response);
        } else {
          setModules([]);
          console.error('Invalid data format for modules:', response);
        }
      } catch (error) {
        console.error('Failed to fetch modules:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch modules. Please try again.',
          variant: 'destructive',
        });
        setModules([]);
      } finally {
        setIsLoadingModules(false);
      }
    };

    fetchModules();
  }, []);

  useEffect(() => {
    if (allLocationRoles) {
      setSelectedRoles((prev) => { return { ...prev, country: allLocationRoles.reduce((acc, roleObject) => acc.concat(roleObject.roles), []) } })
    }
  }, [allLocationRoles])

  // Fetch user location roles when user is selected (for the table display)
  useEffect(() => {
    if (selectedUser) {
      getUserLocationRole(selectedUser);
    } else {
      // Clear location roles when no user is selected
      setAllLocationRoles([]);
    }
  }, [selectedUser]);
  // Function to determine if roles should be shown based on current location selection
  const shouldShowRoles = () => {
    // If no user is selected, don't show roles
    if (!selectedUser) return false;

    // Always show roles if any "all" option is selected at any level
    if (selectedLocation.country === 'tier1-all') {
      return true; // Show roles for all countries
    }

    if (selectedLocation.region === 'tier2-all') {
      return true; // Show roles for all regions under selected country
    }

    if (selectedLocation.site === 'tier3-all') {
      return true; // Show roles for all sites under selected region
    }

    if (selectedLocation.level === 'tier4-all') {
      return true; // Show roles for all level 4 under selected site
    }

    // Always show roles if we have a level 5 selection (deepest level)
    if (selectedLocation.sublevel) {
      return true; // Any level 5 selection
    }

    // For specific selections, only show roles if we've reached the deepest available level
    // Use the sub-level availability information from FilterLocation component

    // If we have a specific level 4 selection, only show roles if no level 5 is available
    if (selectedLocation.level && selectedLocation.level !== 'tier4-all') {
      return !subLevelsAvailable.tier5Available; // Show roles only if no tier 5 available
    }

    // If we have a specific site selection, only show roles if no level 4 is available
    if (selectedLocation.site && selectedLocation.site !== 'tier3-all') {
      return !subLevelsAvailable.tier4Available; // Show roles only if no tier 4 available
    }

    // If we have a specific region selection, only show roles if no site is available
    if (selectedLocation.region && selectedLocation.region !== 'tier2-all') {
      return !subLevelsAvailable.tier3Available; // Show roles only if no tier 3 available
    }

    // If we have a specific country selection, only show roles if no region is available
    if (selectedLocation.country && selectedLocation.country !== 'tier1-all') {
      return !subLevelsAvailable.tier2Available; // Show roles only if no tier 2 available
    }

    return false;
  };

  // Fetch individual user roles when location selection changes
  useEffect(() => {
    if (shouldShowRoles()) {
      getIndividualRoles();
    } else {
      // Clear individual roles if conditions are not met
      setIndividualSelectedRole({ roles: [], disabledRoles: [] });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedUser, selectedLocation.country, selectedLocation.region, selectedLocation.site, selectedLocation.level]);

  // Mock data for current assignments
  const [roleAssignments, setRoleAssignments] = useState<RoleAssignment[]>([]);

  // Handle role checkbox change
  const handleRoleChange = (
    e: { target: { value: string; checked: boolean } },
    category: string
  ) => {
    const roleId = e.target.value;
    const checked = e.target.checked;

    console.log(roleId);

    // Update individualSelectedRole
    setIndividualSelectedRole((prevRoles) => {
      if (checked) {
        // Add the role to the selected roles if it's not already there
        if (!prevRoles.roles.includes(roleId)) {
          return { ...prevRoles, roles: [...prevRoles.roles, roleId] };
        }
      } else {
        // Remove the role from the selected roles
        return { ...prevRoles, roles: prevRoles.roles.filter((id) => id !== roleId) };
      }
      return prevRoles;
    });

    // Update selectedRoles
    setSelectedRoles((prevRoles) => {
      // Initialize the category roles array if it doesn't exist
      const categoryRoles = prevRoles[category] || [];

      console.log(category, prevRoles, categoryRoles, 'check');

      if (checked) {
        // Add the role to the selected roles if it's not already there
        if (!categoryRoles.includes(roleId)) {
          return {
            ...prevRoles,
            [category]: [...categoryRoles, roleId],
          };
        }
      } else {
        // Remove the role from the selected roles
        return {
          ...prevRoles,
          [category]: categoryRoles.filter((id) => id !== roleId),
        };
      }
      return prevRoles;
    });
  };

  // Open form reset confirmation dialog
  const openResetFormConfirmDialog = () => {
    if (!selectedUser) {
      // If no user is selected, just reset the form without confirmation
      resetFormFields();
      return;
    }

    setResetFormConfirmDialogOpen(true);
  };

  // Reset form fields without API call
  const resetFormFields = () => {
    const emptyLocation = {
      country: "",
      region: "",
      site: "",
      level: "",
      sublevel: ""
    };
    setSelectedLocation(emptyLocation);

    // Reset the FilterLocation component
    handleLocationFilter("", "", "", "", "");

    // Reset selected roles only
    // NOTE: We don't reset allLocationRoles or individualSelectedRole here because we want to keep the fetched data for display
    setSelectedRoles({});
  };

  // Reset the form with API call
  const resetForm = async () => {
    // Check if a user is selected
    if (selectedUser) {
      try {
        // Close the confirmation dialog
        setResetFormConfirmDialogOpen(false);

        // Show processing message
        toast({
          title: "Processing",
          description: "Resetting user location roles...",
        });

        // Call the API to reset the user location roles
        console.log(`Resetting user location roles with DELETE request to: ${USER_LOCATION_ROLE_WITH_ID_URL(selectedUser)}`);

        // Use the delete method to reset the roles
        await api.delete(USER_LOCATION_ROLE_WITH_ID_URL(selectedUser));

        // Show success toast
        toast({
          title: "Success",
          description: "User location roles have been reset",
        });

        // Refresh the user location roles
        await getUserLocationRole(selectedUser);
      } catch (error) {
        console.error('Failed to reset user location roles:', error);
        toast({
          title: "Error",
          description: "Failed to reset user location roles. Please try again.",
          variant: "destructive",
        });
      }
    }

    // Reset form fields
    resetFormFields();
  };

  // Parse location string into Location object
  const parseLocationString = (locationString: string): Location => {
    if (locationString === "Global") {
      return {
        country: "",
        region: "",
        site: "",
        level: "",
        sublevel: ""
      };
    }

    const parts = locationString.split(" > ");
    return {
      country: parts[0] || "",
      region: parts[1] || "",
      site: parts[2] || "",
      level: parts[3] || "",
      sublevel: parts[4] || ""
    };
  };

  // Find module ID by name
  const findModuleIdByName = (moduleName: string): string => {
    const module = modules.find(m => m.name === moduleName);
    return module?.id || "";
  };

  // Find role IDs by names
  const findRoleIdsByNames = (moduleId: string, roleNames: string[]): string[] => {
    const module = modules.find(m => m.id === moduleId);
    if (!module) return [];

    return roleNames.map(roleName => {
      const role = module.roles.find(r => r.name === roleName);
      return role?.id || "";
    }).filter(Boolean);
  };

  // Fetch user custom roles
  const fetchUserCustomRoles = async (userId: string) => {
    try {
      const userData = await api.get(USERS_URL_WITH_ID(userId)) as Record<string, any>;

      if (userData.customRoles) {
        setCustomRoles(userData.customRoles as Record<string, string[]>);
      } else {
        // Set default empty custom roles structure
        setCustomRoles({
          country: [],
          ehs: [],
          eptw: [],
          incident: [],
          inspection: [],
          plant: [],
          groupEhs: [],
          report: []
        });
      }

      return true;
    } catch (error) {
      console.error('Failed to fetch user custom roles:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch user custom roles. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Fetch user location roles
  const getUserLocationRole = async (userId: string) => {
    try {
      const data = await api.get(GET_MY_USER_LOCATION_ROLE_URL(userId));

      // Type assertion to handle the response
      const locationRoles = Array.isArray(data)
        ? data.map(item => item as LocationRole)
        : [];

      setAllLocationRoles(locationRoles);

      return true;
    } catch (error) {
      console.error('Failed to fetch user location roles:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch user location roles. Please try again.',
        variant: 'destructive',
      });
      setAllLocationRoles([]);
      return false;
    }
  };

  // Get role name by ID from modules
  const getRoleNameById = (roleId: string): string => {
    for (const module of modules) {
      const role = module.roles?.find(r => r.id === roleId);
      if (role) {
        return role.name;
      }
    }
    return 'Unknown Role';
  };

  // Get location name by ID (matching test.js implementation)
  const getLocationName = (id: string, locationArray: any[], allText: string): string => {
    if (id === "") return "";
    if (id.endsWith("-all")) return allText;
    const location = locationArray.find((location: any) => location.id === id);
    return location ? location.name : 'Unknown';
  };

  // Open assign permissions dialog for new assignment
  const openAssignDialog = async () => {
    if (!selectedUser) {
      toast({
        title: "Error",
        description: "Please select a user first",
        variant: "destructive"
      });
      return;
    }

    // Fetch user custom roles and location roles
    const customRolesSuccess = await fetchUserCustomRoles(selectedUser);
    const locationRolesSuccess = await getUserLocationRole(selectedUser);



    if (customRolesSuccess && locationRolesSuccess) {
      setIsEditing(false);
      setEditingAssignmentId(null);
      setEditingModuleId(null);

      // Don't reset form fields when opening dialog - we want to preserve any existing location selection
      // Only reset selectedRoles to start fresh
      setSelectedRoles({});

      // Open the assign dialog
      setAssignDialogOpen(true);
    }
  };

  // Open assign permissions dialog for editing
  const openEditDialog = async (assignmentId: string) => {
    const assignment = roleAssignments.find(a => a.id === assignmentId);
    if (!assignment) return;

    // Set the user
    setSelectedUser(assignment.user.id);

    // Fetch user custom roles and location roles
    const customRolesSuccess = await fetchUserCustomRoles(assignment.user.id);
    const locationRolesSuccess = await getUserLocationRole(assignment.user.id);

    if (!customRolesSuccess || !locationRolesSuccess) {
      return;
    }

    // Set editing state
    setIsEditing(true);
    setEditingAssignmentId(assignmentId);

    // Parse location
    const locationObj = parseLocationString(assignment.location);
    setSelectedLocation(locationObj);

    // This will trigger the FilterLocation component to update
    handleLocationFilter(
      locationObj.country,
      locationObj.region,
      locationObj.site,
      locationObj.level,
      locationObj.sublevel
    );

    // Find module ID
    const moduleId = findModuleIdByName(assignment.module);
    if (!moduleId) {
      toast({
        title: "Error",
        description: "Could not find module for editing",
        variant: "destructive"
      });
      return;
    }

    // Set the editing module ID
    setEditingModuleId(moduleId);

    // Find role IDs
    const roleIds = findRoleIdsByNames(moduleId, assignment.roles);

    // Set selected roles
    setSelectedRoles({ [moduleId]: roleIds });

    // Open dialog
    setAssignDialogOpen(true);

    toast({
      title: "Info",
      description: `Editing permissions for ${assignment.module}`,
    });
  };

  // Handle assign permissions
  const handleAssignPermissions = async () => {
    // Find the selected user
    const user = users.find(u => u.id === selectedUser);
    if (!user) return;

    // Create location string
    const locationParts = [
      selectedLocation.country,
      selectedLocation.region,
      selectedLocation.site,
      selectedLocation.level,
      selectedLocation.sublevel
    ].filter(Boolean);

    const locationString = locationParts.length > 0
      ? locationParts.join(" > ")
      : "Global";

    // Submit the role assignments to the API
    try {
      // First, submit the individual user location roles
      await api.post(INDIVIDUAL_USER_LOCATION_ROLE_URL, {
        userId: selectedUser,
        roles: individualSelectedRole.roles,
        locations: {
          locationOne: selectedLocation.country,
          locationTwo: selectedLocation.region,
          locationThree: selectedLocation.site,
          locationFour: selectedLocation.level
        }
      });

      // If we get here without an error, the POST was successful

      // Then, update the user's custom roles
      // For PATCH requests, if no error is thrown, it means success (even with 204 status)
      await api.patch(USERS_URL_WITH_ID(selectedUser), {
        customRoles: selectedRoles
      });

      // If we reach here, both API calls were successful
      // Refresh the user location roles
      await getUserLocationRole(selectedUser);

      // Update the UI with the new assignment
      if (isEditing && editingAssignmentId) {
        // Update existing assignment
        setRoleAssignments(prev => {
          return prev.map(assignment => {
            if (assignment.id !== editingAssignmentId) return assignment;

            // Get the first module ID (we're only editing one module at a time)
            const moduleId = Object.keys(selectedRoles)[0];
            if (!moduleId) return assignment;

            const module = modules.find(m => m.id === moduleId);
            if (!module) return assignment;

            const roleIds = selectedRoles[moduleId] || [];
            const roleNames = roleIds.map(roleId => {
              const role = module.roles.find(r => r.id === roleId);
              return role?.name || "";
            }).filter(Boolean);

            return {
              ...assignment,
              location: locationString,
              roles: roleNames,
              assignedDate: new Date().toLocaleDateString() // Update the date
            };
          });
        });

        toast({
          title: "Success",
          description: "Permissions updated successfully",
        });
      } else {
        // Create new assignments for each module with selected roles
        Object.entries(selectedRoles).forEach(([moduleId, roleIds]) => {
          if (roleIds.length === 0) return;

          const module = modules.find(m => m.id === moduleId);
          if (!module) return;

          const roleNames = roleIds.map(roleId => {
            const role = module.roles.find(r => r.id === roleId);
            return role?.name || "";
          }).filter(Boolean);

          if (roleNames.length === 0) return;

          // Create a new assignment
          const newAssignment: RoleAssignment = {
            id: Date.now().toString() + moduleId,
            user,
            module: module.name,
            location: locationString,
            roles: roleNames,
            assignedDate: new Date().toLocaleDateString()
          };

          setRoleAssignments(prev => [...prev, newAssignment]);
        });

        toast({
          title: "Success",
          description: "Permissions assigned successfully",
        });
      }

      // Reset state and close dialog
      setIsEditing(false);
      setEditingAssignmentId(null);
      setEditingModuleId(null);
      setAssignDialogOpen(false);
    } catch (error) {
      console.error('Failed to assign permissions:', error);
      toast({
        title: "Error",
        description: "Failed to assign permissions. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle delete assignment
  const handleDeleteAssignment = (id: string) => {
    // Find the assignment to show in the alert
    const assignment = roleAssignments.find(a => a.id === id);

    setRoleAssignments(prev => prev.filter(assignment => assignment.id !== id));

    toast({
      title: "Success",
      description: `Assignment for ${assignment?.module || 'module'} deleted successfully`,
    });
  };

  // Open reset confirmation dialog
  const openResetConfirmDialog = (id: string) => {
    setResetAssignmentId(id);
    setResetConfirmDialogOpen(true);
  };

  // Handle reset assignment
  const handleResetAssignment = async () => {
    // Make sure we have a valid assignment ID
    if (!resetAssignmentId) {
      setResetConfirmDialogOpen(false);
      return;
    }

    // Find the assignment to reset
    const assignmentIndex = roleAssignments.findIndex(a => a.id === resetAssignmentId);
    if (assignmentIndex === -1) {
      setResetConfirmDialogOpen(false);
      return;
    }

    const assignment = roleAssignments[assignmentIndex];

    try {
      // Close the confirmation dialog
      setResetConfirmDialogOpen(false);

      // Show processing message
      toast({
        title: "Processing",
        description: "Resetting user location roles...",
      });

      // Extract the actual role ID from the assignment
      // In a real implementation, you would need to get the actual role location ID
      // For now, we'll use the assignment ID as provided
      const roleLocationId = resetAssignmentId;

      // Call the API to reset the user location roles
      console.log(`Resetting user location roles with DELETE request to: ${USER_LOCATION_ROLE_WITH_ID_URL(roleLocationId)}`);

      // Use the delete method to reset the roles
      await api.delete(USER_LOCATION_ROLE_WITH_ID_URL(roleLocationId));

      // Update the UI to reflect the changes
      setRoleAssignments(prev => {
        const updated = [...prev];
        updated[assignmentIndex] = {
          ...assignment,
          roles: [], // Reset to no roles
          assignedDate: new Date().toLocaleDateString() // Update the date
        };
        return updated;
      });

      // Refresh the user location roles if the current user is the one being reset
      if (assignment.user.id === selectedUser) {
        await getUserLocationRole(selectedUser);
      }

      // Show success toast
      toast({
        title: "Success",
        description: `Roles for ${assignment.module} have been reset`,
      });
    } catch (error) {
      console.error('Failed to reset user location roles:', error);
      toast({
        title: "Error",
        description: "Failed to reset user location roles. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Clear the reset assignment ID
      setResetAssignmentId(null);
    }
  };

  // Pagination for assignments table
  const {
    paginatedData: paginatedAssignments,
    currentPage,
    totalPages,
    goToPage
  } = usePagination({
    data: roleAssignments,
    itemsPerPage: 10
  });

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title="Project Role Assignment"
          subtitle="Configure user roles and specific permissions for different modules of the system"
        />

        <div className="space-y-6">
          {/* User Selection */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">User Selection</h3>
                <div className="w-full max-w-xs">
                  <Label htmlFor="user-select">Select User</Label>
                  <Select
                    value={selectedUser}
                    onValueChange={setSelectedUser}
                  >
                    <SelectTrigger id="user-select" className="w-full">
                      <SelectValue placeholder="Select user" />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoadingUsers ? (
                        <div className="flex items-center justify-center p-4">
                          <Loader2 className="h-5 w-5 animate-spin text-blue-500 mr-2" />
                          <span>Loading users...</span>
                        </div>
                      ) : users.length > 0 ? (
                        users.map(user => (
                          <SelectItem key={user.id} value={user.id}>
                            <div>
                              <div>{user.firstName} ({user.email})</div>

                            </div>
                          </SelectItem>
                        ))
                      ) : (
                        <div className="p-4 text-center text-gray-500">No users found</div>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end">
                  <Button
                    onClick={openAssignDialog}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Assign Permissions
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* List of Assigned Roles Table - Show when user is selected */}
          {selectedUser && (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">List of Assigned Roles</h3>
                  <hr className="border-gray-300" />

                  {allLocationRoles.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse border border-gray-300">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border border-gray-300 px-4 py-2 text-left font-medium">Location</th>
                            <th className="border border-gray-300 px-4 py-2 text-left font-medium">Assigned Roles</th>
                            <th className="border border-gray-300 px-4 py-2 text-left font-medium">Date Assigned</th>
                          </tr>
                        </thead>
                        <tbody>
                          {allLocationRoles.map((locationRole: LocationRole, index: number) => {
                            // Initialize an array to hold the names for each level (matching test.js)
                            let locationNames: string[] = [];
                            const roleNames = locationRole.roles.map((roleId: string) => getRoleNameById(roleId));

                            // Function to get name by ID or handle special 'all' cases (matching test.js exactly)
                            const getLocationNameLocal = (id: string, locationArray: { id: string; name: string }[], allText: string): string => {
                              if (id === "") return ""; // Return empty if ID is not set
                              if (id.endsWith("-all")) return allText; // Handle 'all' cases
                              const location = locationArray.find((location: { id: string; name: string }) => location.id === id);
                              return location ? location.name : 'Unknown';
                            };

                            // Get names for each level (matching test.js exactly)
                            locationNames.push(getLocationNameLocal(locationRole.locationOneId || "", country, 'All'));
                            locationNames.push(getLocationNameLocal(locationRole.locationTwoId || "", locationTwo, 'All'));
                            locationNames.push(getLocationNameLocal(locationRole.locationThreeId || "", locationThree, 'All'));
                            locationNames.push(getLocationNameLocal(locationRole.locationFourId || "", locationFour, 'All'));

                            // Filter out empty or unknown locations before joining (matching test.js)
                            locationNames = locationNames.filter(name => name && name !== 'Unknown');

                            return (
                              <tr key={index} className="hover:bg-gray-50">
                                <td className="border border-gray-300 px-4 py-2">
                                  <span className="font-medium text-sm">
                                    {locationNames.join(' > ') || 'Global'}
                                  </span>
                                </td>
                                <td className="border border-gray-300 px-4 py-2">
                                  <div className="space-y-1">
                                    {roleNames.map((name: string, roleIndex: number) => (
                                      <span key={roleIndex} className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1">
                                        {name}
                                      </span>
                                    ))}
                                  </div>
                                </td>
                                <td className="border border-gray-300 px-4 py-2">
                                  <span className="text-sm text-gray-600">
                                    {locationRole.created ? new Date(locationRole.created).toLocaleDateString() : 'N/A'}
                                  </span>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-8 border rounded-md bg-gray-50">
                      <p className="text-gray-500 text-sm">No assigned roles found for this user</p>
                      <p className="text-gray-400 text-xs mt-1">Click "Assign Permissions" to add roles</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          
        </div>
      </div>

      {/* Permission Assignment Dialog */}
      <Dialog open={assignDialogOpen} onOpenChange={setAssignDialogOpen}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold flex items-center justify-between">
              <span>
                {isEditing ? "Edit" : "Assign"} Permissions to {
                  (() => {
                    const user = users.find(u => u.id === selectedUser);
                    return user ? `${user.firstName} (${user.email})` : "User";
                  })()
                }
              </span>
              {/* <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full"
                onClick={() => setAssignDialogOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button> */}
            </DialogTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {isEditing
                ? "Edit location and roles for this module"
                : "Select location and roles to assign to this user"}
            </p>
          </DialogHeader>

          {/* Location Selection */}
          <div className="mb-6">
            <h3 className="font-medium mb-4">Location</h3>
            <FilterLocation
              handleFilter={handleLocationFilter}
              disableAll={false}
              onSubLevelsAvailable={handleSubLevelsAvailable}
            />
          </div>

          {/* Main Content - Module Roles */}
          <div className="space-y-6">
            <h3 className="font-medium text-lg">Available Roles</h3>
              {isLoadingModules ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500 mr-2" />
                  <span>Loading modules...</span>
                </div>
              ) : modules.length > 0 ? (
                // Show modules when location conditions are met and individual roles are loaded
                (shouldShowRoles() && individualSelectedRole.roles) ? (
                  (isEditing && editingModuleId
                    ? modules.filter(module => module.id === editingModuleId)
                    : modules
                  ).map((module, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <h4 className="mb-3 font-medium text-base">{module.name}</h4>
                    <div className="grid grid-cols-3 gap-2">
                      {module.roles && module.roles.map((role, roleIndex) => {
                        // Check if role is disabled based on individualSelectedRole
                        const isDisabled = individualSelectedRole.disabledRoles.includes(role.id);

                        // Role is checked if it's in individualSelectedRole.roles OR if it's disabled
                        // This matches the test.js logic exactly
                        const isChecked = individualSelectedRole.roles.includes(role.id) || isDisabled;

                        return (
                          <label
                            key={roleIndex}
                            className={`flex items-center space-x-2 p-2 rounded hover:bg-gray-50 ${isDisabled ? 'opacity-50' : ''}`}
                          >
                            <Checkbox
                              id={`role-${role.id}`}
                              value={role.id}
                              checked={isChecked}
                              disabled={isDisabled}
                              onCheckedChange={(checked) => {
                                // Create a synthetic event object to match the expected interface
                                const syntheticEvent = {
                                  target: {
                                    value: role.id,
                                    checked: !!checked
                                  }
                                };
                                handleRoleChange(syntheticEvent, module.maskName || '');
                              }}
                            />
                            <span className="text-sm">{role.name}</span>
                          </label>
                        );
                      })}
                    </div>
                  </div>
                  ))
                ) : (
                  <div className="text-center p-8 border rounded-md bg-gray-50">
                    <p className="text-gray-500">Please select a location to see available roles</p>
                  </div>
                )
              ) : (
                <div className="text-center p-8 border rounded-md bg-gray-50">
                  <p className="text-gray-500">No modules available</p>
                </div>
              )}
          </div>

          <DialogFooter className="mt-6 flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setAssignDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="outline" onClick={openResetFormConfirmDialog} className="text-amber-600 border-amber-600 hover:bg-amber-50">
              Reset All Roles
            </Button>
            <Button onClick={handleAssignPermissions} className="bg-blue-600 hover:bg-blue-700">
              {isEditing ? "Update" : "Assign"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset Confirmation Dialog */}
      <Dialog open={resetConfirmDialogOpen} onOpenChange={setResetConfirmDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Confirm Reset
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to reset all roles for this assignment? This action cannot be undone.</p>
          </div>
          <DialogFooter className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setResetConfirmDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleResetAssignment}
            >
              Reset Roles
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset Form Confirmation Dialog */}
      <Dialog open={resetFormConfirmDialogOpen} onOpenChange={setResetFormConfirmDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Reset User Roles
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Do you want to reset all location roles for this user? This action cannot be undone.</p>
          </div>
          <DialogFooter className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setResetFormConfirmDialogOpen(false)}>
              No, Keep Roles
            </Button>
            <Button
              variant="destructive"
              onClick={resetForm}
            >
              Yes, Reset All Roles
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
