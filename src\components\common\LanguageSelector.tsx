import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe } from 'lucide-react';

interface LanguageSelectorProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showLabel?: boolean;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'outline',
  size = 'default',
  showLabel = true,
}) => {
  const { languages, currentLanguage, changeLanguage } = useLanguage();

  // Filter only enabled languages
  const enabledLanguages = languages.filter(lang => lang.enabled);

  // For debugging
  // console.log("Available languages:", languages);
  // console.log("Enabled languages:", enabledLanguages);
  // console.log("Current language:", currentLanguage);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size}>
          <Globe className="h-4 w-4 mr-2" />
          {showLabel && currentLanguage.name}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {enabledLanguages.map((language) => (
          <DropdownMenuItem
            key={language.id}
            onClick={() => changeLanguage(language.code)}
            className={language.code === currentLanguage.code ? 'bg-accent' : ''}
          >
            {language.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LanguageSelector;
