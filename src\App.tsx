
import { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { UserProvider } from "@/contexts/UserContext";
import { SidebarProvider } from "@/contexts/SidebarContext";
import { AuthProvider } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import RouteTracker from "@/components/navigation/RouteTracker";

// Import i18n configuration
import "@/lib/i18n";

// Import pages
import Index from "./pages/Index";
import Login from "./pages/Login";
import Modules from "./pages/Modules";
import EntityHierarchy from "./pages/EntityHierarchy";
import WorkAreas from "./pages/WorkAreas";
import WorkActivities from "./pages/WorkActivities";
import Languages from "./pages/Languages";
import UserManagement from "./pages/UserManagement";
import RoleAssignment from "./pages/RoleAssignment";
import ActivityLog from "./pages/ActivityLog";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <Suspense fallback={<div className="flex h-screen w-screen items-center justify-center">Loading...</div>}>
      <LanguageProvider>
        <AuthProvider>
          <UserProvider>
            <SidebarProvider>
              <TooltipProvider>
                <Toaster />
                <Sonner />
                <BrowserRouter>
                  <RouteTracker>
                    <Routes>
                      <Route path="/login" element={<Login />} />
                      <Route path="/" element={
                        <ProtectedRoute>
                          <Index />
                        </ProtectedRoute>
                      } />
                      <Route path="/modules" element={
                        <ProtectedRoute>
                          <Modules />
                        </ProtectedRoute>
                      } />
                      <Route path="/entity-hierarchy" element={
                        <ProtectedRoute>
                          <EntityHierarchy />
                        </ProtectedRoute>
                      } />
                      <Route path="/departments" element={
                        <ProtectedRoute>
                          <WorkAreas />
                        </ProtectedRoute>
                      } />
                      <Route path="/work-activities" element={
                        <ProtectedRoute>
                          <WorkActivities />
                        </ProtectedRoute>
                      } />
                      <Route path="/languages" element={
                        <ProtectedRoute>
                          <Languages />
                        </ProtectedRoute>
                      } />
                      <Route path="/users" element={
                        <ProtectedRoute>
                          <UserManagement />
                        </ProtectedRoute>
                      } />
                      <Route path="/role-assignment" element={
                        <ProtectedRoute>
                          <RoleAssignment />
                        </ProtectedRoute>
                      } />
                      <Route path="/activity" element={
                        <ProtectedRoute>
                          <ActivityLog />
                        </ProtectedRoute>
                      } />
                      <Route path="/settings" element={
                        <ProtectedRoute>
                          <Settings />
                        </ProtectedRoute>
                      } />
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </RouteTracker>
                </BrowserRouter>
              </TooltipProvider>
            </SidebarProvider>
          </UserProvider>
        </AuthProvider>
      </LanguageProvider>
    </Suspense>
  </QueryClientProvider>
);

export default App;
