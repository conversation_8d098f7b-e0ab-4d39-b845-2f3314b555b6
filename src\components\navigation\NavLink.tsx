import React from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface NavLinkProps {
  to: string;
  className?: string;
  children: React.ReactNode;
  onClick?: (e: React.MouseEvent) => void;
}

const NavLink: React.FC<NavLinkProps> = ({ to, className, children, onClick }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleClick = (e: React.MouseEvent) => {
    // If there's a custom onClick handler, call it
    if (onClick) {
      onClick(e);
      return;
    }

    // Prevent default Link behavior
    e.preventDefault();

    // If we're already on this path, force a navigation
    if (location.pathname === to) {
      console.log(`Already at ${to}, forcing navigation`);
      navigate(to, { replace: true });
    } else {
      // Otherwise, navigate normally
      navigate(to);
    }
  };

  return (
    <Link to={to} className={className} onClick={handleClick}>
      {children}
    </Link>
  );
};

export default NavLink;
