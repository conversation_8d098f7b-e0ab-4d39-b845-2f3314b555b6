import { api } from './apiService';
import storageService from './storageService';

// Define the Module interface based on the expected API response
export interface ApiModule {
  id: string;
  name: string;
  description: string;
  status: boolean; // API returns boolean for status
  type: string;
  icon?: string;
  color?: string;
}

// Define the Dropdown interface based on the expected API response
export interface Dropdown {
  id: string;
  name: string;
  maskName: string;
  levels: string[];
  maskId: string;
  description: string;
  created: string;
  updated: string;
  serviceId: string;
}

// Define our internal Module interface
export interface Module {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
  type: 'risk' | 'observation' | 'tasks' | 'permit' | 'incident' | 'knowledge' | 'inspection';
  icon?: string;
  color?: string;
}

// API endpoint for modules
const MODULES_ENDPOINT = '/services';

/**
 * Fetch all modules from the API
 * @returns Promise with array of modules
 */
export const fetchModules = async (): Promise<Module[]> => {
  try {
    console.log('Fetching modules from API');

    // Use our API service to fetch modules
    const data = await api.get<ApiModule[]>(MODULES_ENDPOINT);

    console.log('Modules fetched successfully:', data);

    // Map the response to our Module interface
    // Convert boolean status to 'active' or 'inactive' string
    const modules: Module[] = data.map((module: ApiModule) => ({
      id: module.id,
      name: module.name,
      description: module.description || 'No description available',
      status: module.status === true ? 'active' as const : 'inactive' as const, // Convert boolean to string with type assertion
      type: module.type as 'risk' | 'observation' | 'tasks' | 'permit' | 'incident' | 'knowledge' | 'inspection',
      icon: module.icon,
      color: module.color
    }));

    return modules;
  } catch (error) {
    console.error('Failed to fetch modules:', error);

    // Return an empty array or throw an error based on your error handling strategy
    return [];
  }
};

// Storage keys
const STORAGE_KEY_MODULES = 'modules';

// Function to get cached modules
export const getCachedModules = (): Module[] | null => {
  // Use the storage service to get cached modules
  return storageService.getItem<Module[]>(STORAGE_KEY_MODULES);
};

// Function to cache modules
export const cacheModules = (modules: Module[]): void => {
  // Use the storage service to cache modules with expiration
  storageService.setItem(STORAGE_KEY_MODULES, modules, storageService.DEFAULT_EXPIRY);
};

/**
 * Get modules with caching
 * @returns Promise with array of modules
 */
export const getModules = async (): Promise<Module[]> => {
  // Check if we have cached modules
  const cachedModules = getCachedModules();

  if (cachedModules) {
    return cachedModules;
  }

  // Fetch from API if no cache
  const modules = await fetchModules();

  // Cache the modules
  if (modules.length > 0) {
    cacheModules(modules);
  }

  return modules;
};

/**
 * Fetch dropdowns for a specific module
 * @param moduleId The ID of the module to fetch dropdowns for
 * @returns Promise with array of dropdowns
 */
export const fetchModuleDropdowns = async (moduleId: string): Promise<Dropdown[]> => {
  try {
    console.log(`Fetching dropdowns for module ${moduleId}`);

    // Use our API service to fetch dropdowns
    const endpoint = `/services/${moduleId}/dropdowns`;
    const data = await api.get<Dropdown[]>(endpoint);

    console.log('Dropdowns fetched successfully:', data);

    return data;
  } catch (error) {
    console.error(`Failed to fetch dropdowns for module ${moduleId}:`, error);

    // Return an empty array or throw an error based on your error handling strategy
    return [];
  }
};

export default {
  fetchModules,
  getModules,
  fetchModuleDropdowns
};
