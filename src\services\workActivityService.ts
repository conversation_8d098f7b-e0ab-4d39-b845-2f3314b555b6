import { api } from './apiService';
import { toast } from '@/components/ui/use-toast';

// API endpoints
const WORK_ACTIVITIES_ENDPOINT = '/work-activities';

// Interface definitions
export interface WorkActivity {
  id: string;
  name: string;
  workAreaId?: string; // Optional, for when associated with a work area
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Fetch all work activities
 * @returns Promise with array of work activities
 */
export const fetchWorkActivities = async (): Promise<WorkActivity[]> => {
  try {
    console.log('Fetching work activities from API');
    const data = await api.get<WorkActivity[]>(WORK_ACTIVITIES_ENDPOINT);
    console.log('Work activities fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('Failed to fetch work activities:', error);
    toast({
      title: 'Error',
      description: 'Failed to fetch work activities. Please try again.',
      variant: 'destructive',
    });
    return [];
  }
};

/**
 * Create a new work activity
 * @param name The name of the work activity to create
 * @returns Promise with the created work activity
 */
export const createWorkActivity = async (name: string): Promise<WorkActivity | null> => {
  try {
    console.log('Creating new work activity:', name);
    const response = await api.post<WorkActivity>(WORK_ACTIVITIES_ENDPOINT, { name });

    console.log('Work activity created successfully:', response);
    toast({
      title: 'Success',
      description: 'Work activity created successfully.',
    });

    return response;
  } catch (error) {
    console.error('Failed to create work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to create work activity. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Update a work activity
 * @param id The ID of the work activity to update
 * @param name The new name for the work activity
 * @returns Promise with success flag
 */
export const updateWorkActivity = async (id: string, name: string): Promise<boolean> => {
  try {
    console.log(`Updating work activity ${id} with name:`, name);
    await api.patch<any>(`${WORK_ACTIVITIES_ENDPOINT}/${id}`, { name });

    console.log('Work activity updated successfully');
    toast({
      title: 'Success',
      description: 'Work activity updated successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to update work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to update work activity. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Delete a work activity
 * @param id The ID of the work activity to delete
 * @returns Promise with success flag
 */
export const deleteWorkActivity = async (id: string): Promise<boolean> => {
  try {
    console.log(`Deleting work activity ${id}`);
    await api.delete(`${WORK_ACTIVITIES_ENDPOINT}/${id}`);

    console.log('Work activity deleted successfully');
    toast({
      title: 'Success',
      description: 'Work activity deleted successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to delete work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to delete work activity. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Fetch work activities for a specific work area
 * @param workAreaId The ID of the work area
 * @returns Promise with array of work activities
 */
export const fetchWorkActivitiesByWorkArea = async (workAreaId: string): Promise<WorkActivity[]> => {
  try {
    console.log(`Fetching work activities for work area ${workAreaId}`);
    const endpoint = `/departments/${workAreaId}/work-activities`;
    const data = await api.get<WorkActivity[]>(endpoint);

    // Ensure each activity has the workAreaId
    const activitiesWithWorkAreaId = data.map(activity => ({
      ...activity,
      workAreaId
    }));

    console.log('Work activities fetched successfully:', activitiesWithWorkAreaId);
    return activitiesWithWorkAreaId;
  } catch (error) {
    console.error(`Failed to fetch work activities for work area ${workAreaId}:`, error);
    toast({
      title: 'Error',
      description: 'Failed to fetch work activities. Please try again.',
      variant: 'destructive',
    });
    return [];
  }
};

/**
 * Create a new work activity for a specific work area
 * @param workAreaId The ID of the work area
 * @param name The name of the work activity to create
 * @returns Promise with the created work activity
 */
export const createWorkActivityForWorkArea = async (workAreaId: string, name: string): Promise<WorkActivity | null> => {
  try {
    console.log(`Creating new work activity for work area ${workAreaId}:`, name);
    const endpoint = `/departments/${workAreaId}/work-activities`;
    const response = await api.post<WorkActivity>(endpoint, { name });

    console.log('Work activity created successfully:', response);
    toast({
      title: 'Success',
      description: 'Work activity created successfully.',
    });

    // Ensure the workAreaId is included in the response
    return {
      ...response,
      workAreaId
    };
  } catch (error) {
    console.error('Failed to create work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to create work activity. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};
