import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  WorkArea,
  WorkActivity,
  fetchWorkAreas as fetchWorkAreasAPI,
  createWorkArea as createWorkAreaAPI,
  updateWorkArea as updateWorkAreaAPI,
  deleteWorkArea as deleteWorkAreaAPI,
  fetchWorkActivities as fetchWorkActivitiesAPI,
  createWorkActivity as createWorkActivityAPI,
  updateWorkActivity as updateWorkActivityAPI,
  deleteWorkActivity as deleteWorkActivityAPI
} from '@/services/workAreaService';

// Define the state interface
interface WorkAreaState {
  workAreas: WorkArea[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: WorkAreaState = {
  workAreas: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchWorkAreas = createAsyncThunk(
  'workAreas/fetchWorkAreas',
  async (_, { rejectWithValue }) => {
    try {
      return await fetchWorkAreasAPI();
    } catch (error) {
      return rejectWithValue('Failed to fetch work areas');
    }
  }
);

export const createWorkArea = createAsyncThunk(
  'workAreas/createWorkArea',
  async (name: string, { rejectWithValue }) => {
    try {
      const workArea = await createWorkAreaAPI(name);
      if (!workArea) {
        return rejectWithValue('Failed to create work area');
      }
      return workArea;
    } catch (error) {
      return rejectWithValue('Failed to create work area');
    }
  }
);

export const updateWorkArea = createAsyncThunk(
  'workAreas/updateWorkArea',
  async ({ id, name }: { id: string; name: string }, { rejectWithValue }) => {
    try {
      const success = await updateWorkAreaAPI(id, name);
      if (!success) {
        return rejectWithValue('Failed to update work area');
      }
      return { id, name };
    } catch (error) {
      return rejectWithValue('Failed to update work area');
    }
  }
);

export const deleteWorkArea = createAsyncThunk(
  'workAreas/deleteWorkArea',
  async (id: string, { rejectWithValue }) => {
    try {
      const success = await deleteWorkAreaAPI(id);
      if (!success) {
        return rejectWithValue('Failed to delete work area');
      }
      return id;
    } catch (error) {
      return rejectWithValue('Failed to delete work area');
    }
  }
);

export const fetchWorkActivities = createAsyncThunk(
  'workAreas/fetchWorkActivities',
  async (workAreaId: string, { rejectWithValue }) => {
    try {
      const activities = await fetchWorkActivitiesAPI(workAreaId);
      return { workAreaId, activities };
    } catch (error) {
      return rejectWithValue('Failed to fetch work activities');
    }
  }
);

export const createWorkActivity = createAsyncThunk(
  'workAreas/createWorkActivity',
  async ({ workAreaId, name }: { workAreaId: string; name: string }, { rejectWithValue }) => {
    try {
      const activity = await createWorkActivityAPI(workAreaId, name);
      if (!activity) {
        return rejectWithValue('Failed to create work activity');
      }
      return { workAreaId, activity };
    } catch (error) {
      return rejectWithValue('Failed to create work activity');
    }
  }
);

export const updateWorkActivity = createAsyncThunk(
  'workAreas/updateWorkActivity',
  async ({ workAreaId, activityId, name }: { workAreaId: string; activityId: string; name: string }, { rejectWithValue }) => {
    try {
      const success = await updateWorkActivityAPI(workAreaId, activityId, name);
      if (!success) {
        return rejectWithValue('Failed to update work activity');
      }
      return { workAreaId, activityId, name };
    } catch (error) {
      return rejectWithValue('Failed to update work activity');
    }
  }
);

export const deleteWorkActivity = createAsyncThunk(
  'workAreas/deleteWorkActivity',
  async ({ workAreaId, activityId }: { workAreaId: string; activityId: string }, { rejectWithValue }) => {
    try {
      const success = await deleteWorkActivityAPI(workAreaId, activityId);
      if (!success) {
        return rejectWithValue('Failed to delete work activity');
      }
      return { workAreaId, activityId };
    } catch (error) {
      return rejectWithValue('Failed to delete work activity');
    }
  }
);

// Create the slice
const workAreaSlice = createSlice({
  name: 'workAreas',
  initialState,
  reducers: {
    toggleWorkAreaExpanded: (state, action: PayloadAction<string>) => {
      const workAreaId = action.payload;
      const workArea = state.workAreas.find(area => area.id === workAreaId);
      if (workArea) {
        workArea.expanded = !workArea.expanded;
      }
    },
    clearWorkAreaError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch work areas
      .addCase(fetchWorkAreas.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWorkAreas.fulfilled, (state, action) => {
        state.isLoading = false;
        // Ensure each work area has a workActivities array
        state.workAreas = action.payload.map(area => ({
          ...area,
          workActivities: area.workActivities || []
        }));
      })
      .addCase(fetchWorkAreas.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Create work area
      .addCase(createWorkArea.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWorkArea.fulfilled, (state, action) => {
        state.isLoading = false;
        // Ensure the work area has an empty workActivities array
        state.workAreas.push({
          ...action.payload,
          workActivities: action.payload.workActivities || []
        });
      })
      .addCase(createWorkArea.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Update work area
      .addCase(updateWorkArea.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateWorkArea.fulfilled, (state, action) => {
        state.isLoading = false;
        const { id, name } = action.payload;
        const workArea = state.workAreas.find(area => area.id === id);
        if (workArea) {
          workArea.name = name;
        }
      })
      .addCase(updateWorkArea.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Delete work area
      .addCase(deleteWorkArea.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteWorkArea.fulfilled, (state, action) => {
        state.isLoading = false;
        state.workAreas = state.workAreas.filter(area => area.id !== action.payload);
      })
      .addCase(deleteWorkArea.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Fetch work activities
      .addCase(fetchWorkActivities.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWorkActivities.fulfilled, (state, action) => {
        state.isLoading = false;
        const { workAreaId, activities } = action.payload;
        const workArea = state.workAreas.find(area => area.id === workAreaId);
        if (workArea) {
          workArea.workActivities = activities;
        }
      })
      .addCase(fetchWorkActivities.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Create work activity
      .addCase(createWorkActivity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWorkActivity.fulfilled, (state, action) => {
        state.isLoading = false;
        const { workAreaId, activity } = action.payload;
        const workArea = state.workAreas.find(area => area.id === workAreaId);
        if (workArea && workArea.workActivities) {
          workArea.workActivities.push(activity);
        }
      })
      .addCase(createWorkActivity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Update work activity
      .addCase(updateWorkActivity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateWorkActivity.fulfilled, (state, action) => {
        state.isLoading = false;
        const { workAreaId, activityId, name } = action.payload;
        const workArea = state.workAreas.find(area => area.id === workAreaId);
        if (workArea && workArea.workActivities) {
          const activity = workArea.workActivities.find(act => act.id === activityId);
          if (activity) {
            activity.name = name;
          }
        }
      })
      .addCase(updateWorkActivity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Delete work activity
      .addCase(deleteWorkActivity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteWorkActivity.fulfilled, (state, action) => {
        state.isLoading = false;
        const { workAreaId, activityId } = action.payload;
        const workArea = state.workAreas.find(area => area.id === workAreaId);
        if (workArea && workArea.workActivities) {
          workArea.workActivities = workArea.workActivities.filter(act => act.id !== activityId);
        }
      })
      .addCase(deleteWorkActivity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { toggleWorkAreaExpanded, clearWorkAreaError } = workAreaSlice.actions;

export default workAreaSlice.reducer;
