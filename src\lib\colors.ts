// AZ Brand Color Palette
export const BRAND_COLORS = {
  // Primary Colors
  blue: {
    DEFAULT: '#005284',
    light: '#0F6399',
    dark: '#004270',
    50: '#E6EEF3',
    100: '#BFDBE8',
    200: '#95C6DC',
    300: '#6BB0D0',
    400: '#4C9FC7',
    500: '#005284',
    600: '#004B78',
    700: '#00426A',
    800: '#00395C',
    900: '#002941',
  },
  red: {
    DEFAULT: '#BE1F24',
    light: '#D43339',
    dark: '#A51A1F',
    50: '#F9E8E8',
    100: '#F0C5C6',
    200: '#E69FA1',
    300: '#DC797C',
    400: '#D55F62',
    500: '#BE1F24',
    600: '#AD1C21',
    700: '#99191D',
    800: '#85161A',
    900: '#651114',
  },

  // Neutral Colors
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
};

// Convert hex to HSL for CSS variables
export const hexToHSL = (hex: string): string => {
  // Remove the # if present
  hex = hex.replace('#', '');

  // Convert hex to RGB
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  // Find greatest and smallest channel values
  const cmin = Math.min(r, g, b);
  const cmax = Math.max(r, g, b);
  const delta = cmax - cmin;

  let h = 0;
  let s = 0;
  let l = 0;

  // Calculate hue
  if (delta === 0) {
    h = 0;
  } else if (cmax === r) {
    h = ((g - b) / delta) % 6;
  } else if (cmax === g) {
    h = (b - r) / delta + 2;
  } else {
    h = (r - g) / delta + 4;
  }

  h = Math.round(h * 60);
  if (h < 0) h += 360;

  // Calculate lightness
  l = (cmax + cmin) / 2;

  // Calculate saturation
  s = delta === 0 ? 0 : delta / (1 - Math.abs(2 * l - 1));

  // Convert to percentages
  s = +(s * 100).toFixed(1);
  l = +(l * 100).toFixed(1);

  return `${h} ${s}% ${l}%`;
};

// HSL values for CSS variables
export const HSL_VALUES = {
  blue: {
    primary: hexToHSL(BRAND_COLORS.blue.DEFAULT),
    light: hexToHSL(BRAND_COLORS.blue.light),
    dark: hexToHSL(BRAND_COLORS.blue.dark),
  },
  red: {
    primary: hexToHSL(BRAND_COLORS.red.DEFAULT),
    light: hexToHSL(BRAND_COLORS.red.light),
    dark: hexToHSL(BRAND_COLORS.red.dark),
  },
};
