
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Plus,
  Edit,
  Trash2,
  FolderTree,
  MoreHorizontal,
  Search,
  Info,
  ChevronRight,
  Check,
  X,
  GripVertical
} from "lucide-react";
import { useState, useRef, useEffect } from "react";
import {
  getEntityHierarchyLevels,
  updateEntityHierarchyLevel,
  EntityHierarchyLevel,
  defaultHierarchyLevels as fallbackLevels
} from "@/services/entityHierarchyService";
import locationService, {
  LocationOne,
  LocationTwo,
  LocationThree,
  LocationFour,
  LocationFive
} from "@/services/locationService";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle
} from "@/components/ui/resizable";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  UniqueIdentifier
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { DraggableAttributes } from '@dnd-kit/core';
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';

interface Entity {
  id: string;
  name: string;
  type: string;
  parentId?: string;
  children?: Entity[];
  expanded?: boolean;
  status?: boolean;
}

// Type for any location entity
type AnyLocation = LocationOne | LocationTwo | LocationThree | LocationFour | LocationFive;

const EntityHierarchy = () => {
  // State for API-loaded hierarchy levels
  const [hierarchyLevelData, setHierarchyLevelData] = useState<EntityHierarchyLevel[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Derived state for hierarchy level names (using altTitle)
  const [hierarchyLevels, setHierarchyLevels] = useState<string[]>([]);

  // Fetch hierarchy levels from API
  useEffect(() => {
    const fetchHierarchyLevels = async () => {
      setIsLoading(true);
      try {
        const data = await getEntityHierarchyLevels();

        if (data && data.length > 0) {
          setHierarchyLevelData(data);

          // Extract altTitles to use as level names
          const levelNames = data.map(level => level.altTitle);
          setHierarchyLevels(levelNames);

          // Initialize selectedEntities with the new level names
          const newSelectedEntities: { [key: string]: string | null } = {};
          levelNames.forEach(name => {
            newSelectedEntities[name] = null;
          });
          setSelectedEntities(newSelectedEntities);

          // Initialize newEntityNames with the new level names
          const newEntityNamesObj: { [key: string]: string } = {};
          levelNames.forEach(name => {
            newEntityNamesObj[name] = "";
          });
          setNewEntityNames(newEntityNamesObj);

          // Initialize searchQueries with the new level names
          const newSearchQueries: { [key: string]: string } = {};
          levelNames.forEach(name => {
            newSearchQueries[name] = "";
          });
          setSearchQueries(newSearchQueries);

          setError(null);
        } else {
          // Use fallback levels if API returns empty data
          const fallbackNames = fallbackLevels.map(level => level.altTitle);
          setHierarchyLevels(fallbackNames);
          setError("No hierarchy levels found. Using defaults.");
        }
      } catch (err) {
        console.error('Error fetching hierarchy levels:', err);
        setError('Failed to load hierarchy levels. Using defaults.');

        // Use fallback levels if API fails
        const fallbackNames = fallbackLevels.map(level => level.altTitle);
        setHierarchyLevels(fallbackNames);
      } finally {
        setIsLoading(false);
      }
    };

    fetchHierarchyLevels();
  }, []);

  // Fetch location data and update entities state
  useEffect(() => {
    const fetchLocationData = async () => {
      // Only proceed if we have hierarchy levels
      if (hierarchyLevels.length === 0) return;

      setLoadingStates(prev => ({ ...prev, locationOnes: true }));

      try {
        // Fetch location ones (top level)
        const locationOnes = await locationService.fetchLocationOnes();
        setLocationData(prev => ({ ...prev, locationOnes }));

        // Map location ones to entities
        const locationOneEntities = locationOnes.map(location => ({
          id: location.id,
          name: location.name,
          type: hierarchyLevels[0] || 'LocationOne',
          status: location.status
        }));

        // Update entities state
        setEntities(prev => ({
          ...prev,
          [hierarchyLevels[0] || 'LocationOne']: locationOneEntities
        }));

        // Don't auto-select the first location - let the user click to select
      } catch (error) {
        console.error('Error fetching location data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load location data',
          variant: 'destructive'
        });
      } finally {
        setLoadingStates(prev => ({ ...prev, locationOnes: false }));
      }
    };

    fetchLocationData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hierarchyLevels]);

  // State for editing entity type names
  const [editingLevelIndex, setEditingLevelIndex] = useState<number | null>(null);
  const [editLevelValue, setEditLevelValue] = useState<string>("");
  const editLevelInputRef = useRef<HTMLInputElement>(null);

  // State for location data
  const [locationData, setLocationData] = useState<{
    locationOnes: LocationOne[];
    locationTwos: LocationTwo[];
    locationThrees: LocationThree[];
    locationFours: LocationFour[];
    locationFives: LocationFive[];
  }>({
    locationOnes: [],
    locationTwos: [],
    locationThrees: [],
    locationFours: [],
    locationFives: []
  });

  // Loading states for each level
  const [loadingStates, setLoadingStates] = useState({
    locationOnes: false,
    locationTwos: false,
    locationThrees: false,
    locationFours: false,
    locationFives: false
  });

  // Entities state, will be populated from location data
  const [entities, setEntities] = useState<{ [key: string]: Entity[] }>({});

  // State for selections at each level
  const [selectedEntities, setSelectedEntities] = useState<{ [key: string]: string | null }>({
    "Company": null,
    "Region": null,
    "Country": null,
    "Site": null,
    "Area": null
  });

  // State for new entity inputs
  const [newEntityNames, setNewEntityNames] = useState<{ [key: string]: string }>({
    "Company": "",
    "Region": "",
    "Country": "",
    "Site": "",
    "Area": ""
  });

  // Search state
  const [searchQueries, setSearchQueries] = useState<{ [key: string]: string }>({
    "Company": "",
    "Region": "",
    "Country": "",
    "Site": "",
    "Area": ""
  });

  // Edit mode state
  const [editingEntity, setEditingEntity] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>("");
  const editInputRef = useRef<HTMLInputElement>(null);

  // Delete confirmation state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [entityToDelete, setEntityToDelete] = useState<Entity | null>(null);

  // Drag and drop state
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [draggedEntity, setDraggedEntity] = useState<Entity | null>(null);

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Function to add a new entity
  const handleAddEntity = async (type: string) => {
    if (!newEntityNames[type]) return;

    // Show loading toast
    toast({
      title: "Creating location...",
      description: `Creating new ${type}: ${newEntityNames[type]}`,
    });

    // Determine parent ID based on the selected entity at the previous level
    const levelIndex = hierarchyLevels.indexOf(type);
    let parentId: string | undefined;

    if (levelIndex > 0) {
      const parentType = hierarchyLevels[levelIndex - 1];
      parentId = selectedEntities[parentType] || undefined;
    }

    if (levelIndex > 0 && !parentId) {
      toast({
        title: "Error",
        description: "Please select a parent location first",
        variant: "destructive"
      });
      return;
    }

    try {
      let result: AnyLocation | null = null;

      // Create the location based on the level
      switch (levelIndex) {
        case 0: // First level (LocationOne)
          result = await locationService.createLocationOne(newEntityNames[type]);
          break;
        case 1: // Second level (LocationTwo)
          if (!parentId) throw new Error('No parent location selected');
          result = await locationService.createLocationTwo(newEntityNames[type], parentId);
          break;
        case 2: // Third level (LocationThree)
          if (!parentId) throw new Error('No parent location selected');
          result = await locationService.createLocationThree(newEntityNames[type], parentId);
          break;
        case 3: // Fourth level (LocationFour)
          if (!parentId) throw new Error('No parent location selected');
          result = await locationService.createLocationFour(newEntityNames[type], parentId);
          break;
        case 4: // Fifth level (LocationFive)
          if (!parentId) throw new Error('No parent location selected');
          result = await locationService.createLocationFive(newEntityNames[type], parentId);
          break;
        default:
          throw new Error('Invalid location level');
      }

      if (result) {
        // Success toast
        toast({
          title: "Location created",
          description: `Successfully created ${type}: ${newEntityNames[type]}`,
          variant: "default"
        });

        // Add the new entity to the entities state
        const newEntity: Entity = {
          id: result.id,
          name: result.name,
          type: type,
          parentId,
          status: result.status
        };

        setEntities(prev => ({
          ...prev,
          [type]: [...(prev[type] || []), newEntity]
        }));

        // Clear both the search and new entity inputs
        setNewEntityNames(prev => ({
          ...prev,
          [type]: ""
        }));

        setSearchQueries(prev => ({
          ...prev,
          [type]: ""
        }));

        // Select the newly created entity
        handleEntitySelect(type, result.id);
      } else {
        throw new Error('Failed to create location');
      }
    } catch (error) {
      console.error(`Error creating ${type}:`, error);
      toast({
        title: "Error",
        description: `Failed to create location: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    }
  };

  // Handle entity selection
  const handleEntitySelect = (type: string, entityId: string | null) => {
    const levelIndex = hierarchyLevels.indexOf(type);

    // Update the selected entity for this level
    const newSelectedEntities = { ...selectedEntities, [type]: entityId };

    // Clear selections for all lower levels
    for (let i = levelIndex + 1; i < hierarchyLevels.length; i++) {
      newSelectedEntities[hierarchyLevels[i]] = null;
    }

    setSelectedEntities(newSelectedEntities);
  };

  // Fetch child locations when a parent is selected
  useEffect(() => {
    const fetchChildLocations = async () => {
      // Get the selected location one
      const locationOneId = selectedEntities[hierarchyLevels[0]];
      if (!locationOneId || hierarchyLevels.length < 2) return;

      // Set loading state for location twos
      setLoadingStates(prev => ({ ...prev, locationTwos: true }));

      try {
        // Fetch location twos using the API endpoint pattern
        const locationTwos = await locationService.fetchLocationTwos(locationOneId);
        setLocationData(prev => ({ ...prev, locationTwos }));

        // Map location twos to entities
        const locationTwoEntities = locationTwos.map(location => ({
          id: location.id,
          name: location.name,
          type: hierarchyLevels[1],
          parentId: locationOneId,
          status: location.status
        }));

        // Update entities state
        setEntities(prev => ({
          ...prev,
          [hierarchyLevels[1]]: locationTwoEntities
        }));
      } catch (error) {
        console.error('Error fetching location twos:', error);
        toast({
          title: 'Error',
          description: 'Failed to load second-level locations',
          variant: 'destructive'
        });
      } finally {
        setLoadingStates(prev => ({ ...prev, locationTwos: false }));
      }
    };

    fetchChildLocations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedEntities[hierarchyLevels[0]], hierarchyLevels]);

  // Fetch location threes when a location two is selected
  useEffect(() => {
    const fetchLocationThrees = async () => {
      // Get the selected location two
      const locationTwoId = selectedEntities[hierarchyLevels[1]];
      if (!locationTwoId || hierarchyLevels.length < 3) return;

      // Set loading state for location threes
      setLoadingStates(prev => ({ ...prev, locationThrees: true }));

      try {
        // Fetch location threes using the API endpoint pattern
        const locationThrees = await locationService.fetchLocationThrees(locationTwoId);
        setLocationData(prev => ({ ...prev, locationThrees }));

        // Map location threes to entities
        const locationThreeEntities = locationThrees.map(location => ({
          id: location.id,
          name: location.name,
          type: hierarchyLevels[2],
          parentId: locationTwoId,
          status: location.status
        }));

        // Update entities state
        setEntities(prev => ({
          ...prev,
          [hierarchyLevels[2]]: locationThreeEntities
        }));
      } catch (error) {
        console.error('Error fetching location threes:', error);
        toast({
          title: 'Error',
          description: 'Failed to load third-level locations',
          variant: 'destructive'
        });
      } finally {
        setLoadingStates(prev => ({ ...prev, locationThrees: false }));
      }
    };

    fetchLocationThrees();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedEntities[hierarchyLevels[1]], hierarchyLevels]);

  // Fetch location fours when a location three is selected
  useEffect(() => {
    const fetchLocationFours = async () => {
      // Get the selected location three
      const locationThreeId = selectedEntities[hierarchyLevels[2]];
      if (!locationThreeId || hierarchyLevels.length < 4) return;

      // Set loading state for location fours
      setLoadingStates(prev => ({ ...prev, locationFours: true }));

      try {
        // Fetch location fours using the API endpoint pattern
        const locationFours = await locationService.fetchLocationFours(locationThreeId);
        setLocationData(prev => ({ ...prev, locationFours }));

        // Map location fours to entities
        const locationFourEntities = locationFours.map(location => ({
          id: location.id,
          name: location.name,
          type: hierarchyLevels[3],
          parentId: locationThreeId,
          status: location.status
        }));

        // Update entities state
        setEntities(prev => ({
          ...prev,
          [hierarchyLevels[3]]: locationFourEntities
        }));
      } catch (error) {
        console.error('Error fetching location fours:', error);
        toast({
          title: 'Error',
          description: 'Failed to load fourth-level locations',
          variant: 'destructive'
        });
      } finally {
        setLoadingStates(prev => ({ ...prev, locationFours: false }));
      }
    };

    fetchLocationFours();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedEntities[hierarchyLevels[2]], hierarchyLevels]);

  // Fetch location fives when a location four is selected
  useEffect(() => {
    const fetchLocationFives = async () => {
      // Get the selected location four
      const locationFourId = selectedEntities[hierarchyLevels[3]];
      if (!locationFourId || hierarchyLevels.length < 5) return;

      // Set loading state for location fives
      setLoadingStates(prev => ({ ...prev, locationFives: true }));

      try {
        // Fetch location fives using the API endpoint pattern
        const locationFives = await locationService.fetchLocationFives(locationFourId);
        setLocationData(prev => ({ ...prev, locationFives }));

        // Map location fives to entities
        const locationFiveEntities = locationFives.map(location => ({
          id: location.id,
          name: location.name,
          type: hierarchyLevels[4],
          parentId: locationFourId,
          status: location.status
        }));

        // Update entities state
        setEntities(prev => ({
          ...prev,
          [hierarchyLevels[4]]: locationFiveEntities
        }));
      } catch (error) {
        console.error('Error fetching location fives:', error);
        toast({
          title: 'Error',
          description: 'Failed to load fifth-level locations',
          variant: 'destructive'
        });
      } finally {
        setLoadingStates(prev => ({ ...prev, locationFives: false }));
      }
    };

    fetchLocationFives();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedEntities[hierarchyLevels[3]], hierarchyLevels]);

  // Filter entities based on selections
  const getFilteredEntities = (type: string) => {
    const levelIndex = hierarchyLevels.indexOf(type);

    if (levelIndex === 0) {
      // Company level - show all companies
      return entities[type] || [];
    } else {
      // For other levels, filter based on selected parent
      const parentType = hierarchyLevels[levelIndex - 1];
      const parentId = selectedEntities[parentType];

      if (!parentId) {
        return []; // No parent selected, show nothing
      }

      return (entities[type] || []).filter(entity => entity.parentId === parentId);
    }
  };

  // Filter entities based on search query
  const getSearchFilteredEntities = (type: string) => {
    const filtered = getFilteredEntities(type);
    if (!searchQueries[type]) return filtered;

    return filtered.filter(entity =>
      entity.name.toLowerCase().includes(searchQueries[type].toLowerCase())
    );
  };

  const handleSearch = (type: string, query: string) => {
    setSearchQueries(prev => ({
      ...prev,
      [type]: query
    }));
  };

  // Function to start editing an entity
  const handleStartEdit = (entity: Entity) => {
    setEditingEntity(entity.id);
    setEditValue(entity.name);

    // Focus the input field after it's rendered
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
      }
    }, 10);
  };

  // Function to save edited entity
  const handleSaveEdit = async (entity: Entity) => {
    // If the value is empty, don't save
    if (!editValue.trim()) {
      setEditingEntity(null);
      return;
    }

    // Show loading toast
    toast({
      title: "Updating location...",
      description: `Updating ${entity.type}: ${entity.name} to ${editValue.trim()}`,
    });

    try {
      // Determine the level based on the entity type
      const levelIndex = hierarchyLevels.indexOf(entity.type);
      let apiLevel: string;

      switch (levelIndex) {
        case 0:
          apiLevel = 'locationOne';
          break;
        case 1:
          apiLevel = 'locationTwo';
          break;
        case 2:
          apiLevel = 'locationThree';
          break;
        case 3:
          apiLevel = 'locationFour';
          break;
        case 4:
          apiLevel = 'locationFive';
          break;
        default:
          throw new Error('Invalid location level');
      }

      // Update the location using the API
      const result = await locationService.updateLocation(
        apiLevel as keyof typeof locationService.updateLocation,
        entity.id,
        { name: editValue.trim() }
      );

      if (result) {
        // Success toast
        toast({
          title: "Location updated",
          description: `Successfully updated to: ${editValue.trim()}`,
          variant: "default"
        });

        // Update the entity with the new name
        setEntities(prev => ({
          ...prev,
          [entity.type]: prev[entity.type].map(e =>
            e.id === entity.id ? { ...e, name: editValue.trim() } : e
          )
        }));
      } else {
        throw new Error('Failed to update location');
      }
    } catch (error) {
      console.error(`Error updating ${entity.type}:`, error);
      toast({
        title: "Error",
        description: `Failed to update location: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    } finally {
      // Exit edit mode
      setEditingEntity(null);
    }
  };

  // Function to cancel editing
  const handleCancelEdit = () => {
    setEditingEntity(null);
    setEditValue("");
  };

  // Function to start editing a level name
  const handleStartEditLevel = (index: number) => {
    setEditingLevelIndex(index);

    // Editing the display name (altTitle)
    setEditLevelValue(hierarchyLevels[index]);

    // Focus the display name input field after it's rendered
    setTimeout(() => {
      if (editLevelInputRef.current) {
        editLevelInputRef.current.focus();
      }
    }, 10);
  };

  // Function to save edited level name
  const handleSaveEditLevel = (index: number) => {
    // Saving display name (altTitle)
    if (!editLevelValue.trim()) {
      setEditingLevelIndex(null);
      return;
    }

    // Get the old level name
    const oldLevelName = hierarchyLevels[index];

    // Update the level name
    const newHierarchyLevels = [...hierarchyLevels];
    newHierarchyLevels[index] = editLevelValue.trim();
    setHierarchyLevels(newHierarchyLevels);

    // Update the hierarchyLevelData with the new altTitle
    if (hierarchyLevelData[index]) {
      const levelId = hierarchyLevelData[index].id;
      const newAltTitle = editLevelValue.trim();

      // Show loading toast
      toast({
        title: "Updating hierarchy level...",
        description: `Changing ${oldLevelName} to ${newAltTitle}`,
      });

      // Make the API call to update the altTitle
      updateEntityHierarchyLevel(levelId, { altTitle: newAltTitle })
        .then(result => {
          // Check if the update was successful
          // The result could be an EntityHierarchyLevel object or a success flag
          const isSuccess = result !== null && (
            ('success' in result && result.success === true) ||
            ('id' in result && result.id === levelId)
          );

          if (isSuccess) {
            // Update was successful
            const newHierarchyLevelData = [...hierarchyLevelData];
            newHierarchyLevelData[index] = {
              ...newHierarchyLevelData[index],
              altTitle: newAltTitle
            };
            setHierarchyLevelData(newHierarchyLevelData);

            toast({
              title: "Hierarchy level updated",
              description: `Successfully changed ${oldLevelName} to ${newAltTitle}`,
              variant: "default",
            });

            console.log(`Updated display name for ${oldLevelName} to ${newAltTitle}`);
          } else {
            // Update failed
            toast({
              title: "Update failed",
              description: "Failed to update hierarchy level. Please try again.",
              variant: "destructive",
            });

            // Revert the local change
            const newHierarchyLevels = [...hierarchyLevels];
            newHierarchyLevels[index] = oldLevelName;
            setHierarchyLevels(newHierarchyLevels);
          }
        })
        .catch(error => {
          console.error("Error updating hierarchy level:", error);

          toast({
            title: "Update failed",
            description: "An error occurred while updating the hierarchy level.",
            variant: "destructive",
          });

          // Revert the local change
          const newHierarchyLevels = [...hierarchyLevels];
          newHierarchyLevels[index] = oldLevelName;
          setHierarchyLevels(newHierarchyLevels);
        });
    } else {
      // No API data available, just update local state
      console.log(`Updated display name for ${oldLevelName} to ${editLevelValue.trim()} (local only)`);
    }

    // Update entity types in the entities state
    if (entities[oldLevelName]) {
      // Create a new entities object with the updated key
      const newEntities = { ...entities };
      newEntities[editLevelValue.trim()] = entities[oldLevelName].map(entity => ({
        ...entity,
        type: editLevelValue.trim()
      }));

      // Remove the old key
      delete newEntities[oldLevelName];

      // Update the entities state
      setEntities(newEntities);

      // Update selected entities if needed
      if (selectedEntities[oldLevelName] !== null) {
        const newSelectedEntities = { ...selectedEntities };
        newSelectedEntities[editLevelValue.trim()] = selectedEntities[oldLevelName];
        delete newSelectedEntities[oldLevelName];
        setSelectedEntities(newSelectedEntities);
      }

      // Update new entity names if needed
      if (newEntityNames[oldLevelName]) {
        const newEntityNamesObj = { ...newEntityNames };
        newEntityNamesObj[editLevelValue.trim()] = newEntityNames[oldLevelName];
        delete newEntityNamesObj[oldLevelName];
        setNewEntityNames(newEntityNamesObj);
      }

      // Update search queries if needed
      if (searchQueries[oldLevelName]) {
        const newSearchQueries = { ...searchQueries };
        newSearchQueries[editLevelValue.trim()] = searchQueries[oldLevelName];
        delete newSearchQueries[oldLevelName];
        setSearchQueries(newSearchQueries);
      }
    }

    // Exit edit mode
    setEditingLevelIndex(null);
  };

  // Function to cancel editing level name
  const handleCancelEditLevel = () => {
    setEditingLevelIndex(null);
    setEditLevelValue("");
  };

  // Function to initiate delete process
  const handleInitiateDelete = (entity: Entity) => {
    setEntityToDelete(entity);
    setDeleteDialogOpen(true);
  };

  // Function to confirm and execute deletion
  const handleConfirmDelete = async () => {
    if (!entityToDelete) return;

    const entity = entityToDelete;

    // Show loading toast
    toast({
      title: "Deleting location...",
      description: `Deleting ${entity.type}: ${entity.name}`,
    });

    try {
      // Determine the level based on the entity type
      const levelIndex = hierarchyLevels.indexOf(entity.type);
      let apiLevel: string;

      switch (levelIndex) {
        case 0:
          apiLevel = 'locationOne';
          break;
        case 1:
          apiLevel = 'locationTwo';
          break;
        case 2:
          apiLevel = 'locationThree';
          break;
        case 3:
          apiLevel = 'locationFour';
          break;
        case 4:
          apiLevel = 'locationFive';
          break;
        default:
          throw new Error('Invalid location level');
      }

      // Get parent ID for cache invalidation
      let parentId: string | undefined;
      if (levelIndex > 0) {
        parentId = entity.parentId;
      }

      // Delete the location using the API
      const success = await locationService.deleteLocation(
        apiLevel as keyof typeof locationService.deleteLocation,
        entity.id,
        parentId
      );

      if (success) {
        // Success toast
        toast({
          title: "Location deleted",
          description: `Successfully deleted: ${entity.name}`,
          variant: "default"
        });

        // Get all child entities that need to be deleted
        const getChildIds = (parentId: string): string[] => {
          let childIds: string[] = [];

          // Find the level of the parent entity
          const parentType = entity.type;
          const parentLevelIndex = hierarchyLevels.indexOf(parentType);

          // If this is the last level, there are no children
          if (parentLevelIndex >= hierarchyLevels.length - 1) return childIds;

          // Get the next level type
          const childType = hierarchyLevels[parentLevelIndex + 1];

          // Find all direct children
          const directChildren = entities[childType]?.filter(e => e.parentId === parentId) || [];

          // Add direct children IDs
          childIds = [...childIds, ...directChildren.map(c => c.id)];

          // Recursively add grandchildren IDs
          directChildren.forEach(child => {
            childIds = [...childIds, ...getChildIds(child.id)];
          });

          return childIds;
        };

        // Get all child IDs that need to be deleted
        const childIdsToDelete = getChildIds(entity.id);

        // Delete the entity and all its children from local state
        setEntities(prev => {
          const newEntities = { ...prev };

          // Delete the main entity
          if (newEntities[entity.type]) {
            newEntities[entity.type] = prev[entity.type].filter(e => e.id !== entity.id);
          }

          // Delete all children
          hierarchyLevels.slice(hierarchyLevels.indexOf(entity.type) + 1).forEach(level => {
            if (newEntities[level]) {
              newEntities[level] = prev[level].filter(e => !childIdsToDelete.includes(e.id));
            }
          });

          return newEntities;
        });

        // Clear selection if the deleted entity was selected
        if (selectedEntities[entity.type] === entity.id) {
          handleEntitySelect(entity.type, null);
        }
      } else {
        throw new Error('Failed to delete location');
      }
    } catch (error) {
      console.error(`Error deleting ${entity.type}:`, error);
      toast({
        title: "Error",
        description: `Failed to delete location: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    } finally {
      // Close the dialog and reset state
      setDeleteDialogOpen(false);
      setEntityToDelete(null);
    }
  };

  // Function to cancel deletion
  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setEntityToDelete(null);
  };

  // Function to duplicate an entity
  const handleDuplicateEntity = (entity: Entity) => {
    // Generate new ID
    const newId = `${entity.type.toLowerCase()[0]}-${Date.now()}`;

    // Create a duplicate entity with a new ID and modified name
    const duplicatedEntity = {
      ...entity,
      id: newId,
      name: `${entity.name} (Copy)`
    };

    // Add the duplicated entity
    setEntities(prev => ({
      ...prev,
      [entity.type]: [...prev[entity.type], duplicatedEntity]
    }));
  };

  // Drag and drop handlers
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id);

    // Find the dragged entity
    const entityId = active.id as string;
    let foundEntity: Entity | null = null;

    for (const type of hierarchyLevels) {
      const typeEntities = getSearchFilteredEntities(type);
      foundEntity = typeEntities.find(entity => entity.id === entityId) || null;
      if (foundEntity) break;
    }

    setDraggedEntity(foundEntity);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      setActiveId(null);
      setDraggedEntity(null);
      return;
    }

    // Find which type/column this belongs to
    let entityType: string | null = null;
    for (const type of hierarchyLevels) {
      const typeEntities = getSearchFilteredEntities(type);
      if (typeEntities.some(entity => entity.id === active.id)) {
        entityType = type;
        break;
      }
    }

    if (!entityType) {
      setActiveId(null);
      setDraggedEntity(null);
      return;
    }

    const filteredEntities = getSearchFilteredEntities(entityType);
    const oldIndex = filteredEntities.findIndex(entity => entity.id === active.id);
    const newIndex = filteredEntities.findIndex(entity => entity.id === over.id);

    if (oldIndex !== -1 && newIndex !== -1) {
      // Get all entities for this type (not just filtered ones)
      const allEntities = entities[entityType] || [];

      // Find the actual indices in the full array
      const activeEntity = filteredEntities[oldIndex];
      const overEntity = filteredEntities[newIndex];

      const actualOldIndex = allEntities.findIndex(entity => entity.id === activeEntity.id);
      const actualNewIndex = allEntities.findIndex(entity => entity.id === overEntity.id);

      if (actualOldIndex !== -1 && actualNewIndex !== -1) {
        const newEntities = arrayMove(allEntities, actualOldIndex, actualNewIndex);

        // Update local state immediately for better UX
        setEntities(prev => ({
          ...prev,
          [entityType]: newEntities
        }));

        // Call backend API to persist the reordering
        const handleReorderBackend = async () => {
          try {
            // Determine the API level based on the entity type
            const levelIndex = hierarchyLevels.indexOf(entityType);
            let apiLevel: string;
            let parentId: string | undefined;

            switch (levelIndex) {
              case 0:
                apiLevel = 'locationOne';
                break;
              case 1:
                apiLevel = 'locationTwo';
                parentId = selectedEntities[hierarchyLevels[0]] || undefined;
                break;
              case 2:
                apiLevel = 'locationThree';
                parentId = selectedEntities[hierarchyLevels[1]] || undefined;
                break;
              case 3:
                apiLevel = 'locationFour';
                parentId = selectedEntities[hierarchyLevels[2]] || undefined;
                break;
              case 4:
                apiLevel = 'locationFive';
                parentId = selectedEntities[hierarchyLevels[3]] || undefined;
                break;
              default:
                throw new Error('Invalid location level');
            }

            // Get the new order of entity IDs
            const entityIds = newEntities.map(entity => entity.id);

            // Call the reorder API
            const success = await locationService.reorderLocations(
              apiLevel as keyof typeof locationService.reorderLocations,
              entityIds,
              parentId
            );

            if (success) {
              // Show success toast
              toast({
                title: "Entity reordered",
                description: `Successfully moved ${activeEntity.name}`,
                variant: "default"
              });
            } else {
              // Revert the local change if API call failed
              setEntities(prev => ({
                ...prev,
                [entityType]: allEntities
              }));

              toast({
                title: "Reorder failed",
                description: "Failed to save the new order. Changes have been reverted.",
                variant: "destructive"
              });
            }
          } catch (error) {
            console.error('Error reordering entities:', error);

            // Revert the local change if API call failed
            setEntities(prev => ({
              ...prev,
              [entityType]: allEntities
            }));

            toast({
              title: "Reorder failed",
              description: "An error occurred while saving the new order. Changes have been reverted.",
              variant: "destructive"
            });
          }
        };

        // Call the backend API
        handleReorderBackend();
      }
    }

    setActiveId(null);
    setDraggedEntity(null);
  };

  const getTypeColor = (type: string): string => {
    const index = hierarchyLevels.indexOf(type);

    switch (index) {
      case 0: // First level
        return "bg-sky-100 text-sky-700 border-sky-300";
      case 1: // Second level
        return "bg-blue-100 text-blue-700 border-blue-300";
      case 2: // Third level
        return "bg-green-100 text-green-700 border-green-300";
      case 3: // Fourth level
        return "bg-amber-100 text-amber-700 border-amber-300";
      case 4: // Fifth level
        return "bg-rose-100 text-rose-700 border-rose-300";
      default:
        return "bg-gray-100 text-gray-700 border-gray-300";
    }
  };

  const getTypeHeaderColor = (type: string): string => {
    const index = hierarchyLevels.indexOf(type);

    switch (index) {
      case 0: // First level
        return "bg-sky-50 border-b-sky-200";
      case 1: // Second level
        return "bg-blue-50 border-b-blue-200";
      case 2: // Third level
        return "bg-green-50 border-b-green-200";
      case 3: // Fourth level
        return "bg-amber-50 border-b-amber-200";
      case 4: // Fifth level
        return "bg-rose-50 border-b-rose-200";
      default:
        return "bg-gray-50 border-b-gray-200";
    }
  };

  // Sortable Entity Card Component
  const SortableEntityCard = ({ entity, isSelected }: { entity: Entity, isSelected: boolean }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({ id: entity.id });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.5 : 1,
    };

    return (
      <div ref={setNodeRef} style={style}>
        <EntityCard
          entity={entity}
          isSelected={isSelected}
          dragAttributes={attributes}
          dragListeners={listeners}
          isDragging={isDragging}
        />
      </div>
    );
  };

  // Entity Card Component
  const EntityCard = ({
    entity,
    isSelected,
    dragAttributes,
    dragListeners,
    isDragging
  }: {
    entity: Entity,
    isSelected: boolean,
    dragAttributes?: DraggableAttributes,
    dragListeners?: SyntheticListenerMap | undefined,
    isDragging?: boolean
  }) => {
    const isEditing = editingEntity === entity.id;

    // Handle key press in edit mode
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      e.stopPropagation();
      if (e.key === 'Enter') {
        handleSaveEdit(entity);
      } else if (e.key === 'Escape') {
        handleCancelEdit();
      }
    };

    return (
      <div
        onClick={() => {
          if (!isEditing) {
            handleEntitySelect(entity.type, entity.id);
          }
        }}
        className={cn(
          "group p-3 rounded-md mb-2 flex justify-between items-center transition-all duration-200 ease-in-out border",
          isEditing ? "ring-2 ring-primary shadow-sm" : "",
          isDragging ? "shadow-lg ring-2 ring-primary/50 bg-background/80" : "",
          !isEditing && !isDragging && (
            isSelected ?
              cn(
                "shadow-md cursor-pointer transform scale-[1.02]",
                hierarchyLevels.indexOf(entity.type) === 0 && "bg-sky-100 border-sky-500",
                hierarchyLevels.indexOf(entity.type) === 1 && "bg-blue-100 border-blue-500",
                hierarchyLevels.indexOf(entity.type) === 2 && "bg-green-100 border-green-500",
                hierarchyLevels.indexOf(entity.type) === 3 && "bg-amber-100 border-amber-500",
                hierarchyLevels.indexOf(entity.type) === 4 && "bg-rose-100 border-rose-500"
              ) :
              cn(
                "cursor-pointer hover:shadow-sm",
                hierarchyLevels.indexOf(entity.type) === 0 && "hover:bg-sky-50 hover:border-sky-500 hover:scale-[1.01]",
                hierarchyLevels.indexOf(entity.type) === 1 && "hover:bg-blue-50 hover:border-blue-500 hover:scale-[1.01]",
                hierarchyLevels.indexOf(entity.type) === 2 && "hover:bg-green-50 hover:border-green-500 hover:scale-[1.01]",
                hierarchyLevels.indexOf(entity.type) === 3 && "hover:bg-amber-50 hover:border-amber-500 hover:scale-[1.01]",
                hierarchyLevels.indexOf(entity.type) === 4 && "hover:bg-rose-50 hover:border-rose-500 hover:scale-[1.01]"
              )
          ),
          !isDragging && hierarchyLevels.indexOf(entity.type) === 0 && (!isSelected ? "border-l-4 border-l-sky-500" : ""),
          !isDragging && hierarchyLevels.indexOf(entity.type) === 1 && (!isSelected ? "border-l-4 border-l-blue-500" : ""),
          !isDragging && hierarchyLevels.indexOf(entity.type) === 2 && (!isSelected ? "border-l-4 border-l-green-500" : ""),
          !isDragging && hierarchyLevels.indexOf(entity.type) === 3 && (!isSelected ? "border-l-4 border-l-amber-500" : ""),
          !isDragging && hierarchyLevels.indexOf(entity.type) === 4 && (!isSelected ? "border-l-4 border-l-rose-500" : "")
        )}
      >
        <div className="flex items-center flex-1 min-w-0">
          {/* Drag Handle */}
          {dragAttributes && dragListeners && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div
                    {...dragAttributes}
                    {...dragListeners}
                    className={cn(
                      "flex-shrink-0 mr-2 cursor-grab active:cursor-grabbing transition-all duration-200 rounded p-1",
                      "opacity-0 group-hover:opacity-100 hover:bg-muted/50",
                      isSelected && "opacity-100"
                    )}
                  >
                    <GripVertical size={14} className="text-muted-foreground" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Drag to reorder</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <div className={cn(
            "flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full mr-2 transition-all duration-200",
            hierarchyLevels.indexOf(entity.type) === 0 && (isSelected ? "bg-sky-200" : "bg-sky-100"),
            hierarchyLevels.indexOf(entity.type) === 1 && (isSelected ? "bg-blue-200" : "bg-blue-100"),
            hierarchyLevels.indexOf(entity.type) === 2 && (isSelected ? "bg-green-200" : "bg-green-100"),
            hierarchyLevels.indexOf(entity.type) === 3 && (isSelected ? "bg-amber-200" : "bg-amber-100"),
            hierarchyLevels.indexOf(entity.type) === 4 && (isSelected ? "bg-rose-200" : "bg-rose-100")
          )}>
            <div className={cn(
              "w-2 h-2 rounded-full transition-all duration-200",
              isSelected && "w-3 h-3",
              hierarchyLevels.indexOf(entity.type) === 0 && "bg-sky-500",
              hierarchyLevels.indexOf(entity.type) === 1 && "bg-blue-500",
              hierarchyLevels.indexOf(entity.type) === 2 && "bg-green-500",
              hierarchyLevels.indexOf(entity.type) === 3 && "bg-amber-500",
              hierarchyLevels.indexOf(entity.type) === 4 && "bg-rose-500"
            )}></div>
          </div>

          {isEditing ? (
            <div className="flex-1 min-w-0 flex items-center">
              <Input
                ref={editInputRef}
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                onKeyDown={handleKeyDown}
                className="h-7 text-sm py-1 px-2"
                onClick={(e) => e.stopPropagation()}
                autoFocus
              />
              <div className="flex items-center ml-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 rounded-full text-green-600 hover:bg-green-50 hover:text-green-700"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSaveEdit(entity);
                  }}
                >
                  <Check size={14} />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 rounded-full text-muted-foreground hover:bg-muted/20"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCancelEdit();
                  }}
                >
                  <X size={14} />
                </Button>
              </div>
            </div>
          ) : (
            <span className={cn(
              "text-sm truncate",
              isSelected ? "font-bold" : "font-medium",
              // Use consistent text color regardless of selection state
              "text-gray-900"
            )}>
              {entity.name}
            </span>
          )}
        </div>

        {!isEditing && (
          <div className={cn(
            "flex items-center space-x-1 flex-shrink-0 transition-opacity duration-200",
            isSelected ? "opacity-100" : "opacity-0 group-hover:opacity-100"
          )}>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-7 w-7 rounded-full",
                      isSelected && cn(
                        "bg-white/60 hover:bg-white/90",
                        hierarchyLevels.indexOf(entity.type) === 0 && "text-sky-700 hover:text-sky-800",
                        hierarchyLevels.indexOf(entity.type) === 1 && "text-blue-700 hover:text-blue-800",
                        hierarchyLevels.indexOf(entity.type) === 2 && "text-green-700 hover:text-green-800",
                        hierarchyLevels.indexOf(entity.type) === 3 && "text-amber-700 hover:text-amber-800",
                        hierarchyLevels.indexOf(entity.type) === 4 && "text-rose-700 hover:text-rose-800"
                      )
                    )}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card click
                      handleStartEdit(entity);
                    }}
                  >
                    <Edit size={14} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Edit entity</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-7 w-7 text-destructive hover:bg-destructive/10 hover:text-destructive rounded-full",
                      isSelected && "bg-white/60 hover:bg-white/90"
                    )}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card click
                      handleInitiateDelete(entity);
                    }}
                  >
                    <Trash2 size={14} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Delete entity</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <DropdownMenu>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "h-7 w-7 rounded-full",
                          isSelected && cn(
                            "bg-white/60 hover:bg-white/90",
                            hierarchyLevels.indexOf(entity.type) === 0 && "text-sky-700 hover:text-sky-800",
                            hierarchyLevels.indexOf(entity.type) === 1 && "text-blue-700 hover:text-blue-800",
                            hierarchyLevels.indexOf(entity.type) === 2 && "text-green-700 hover:text-green-800",
                            hierarchyLevels.indexOf(entity.type) === 3 && "text-amber-700 hover:text-amber-800",
                            hierarchyLevels.indexOf(entity.type) === 4 && "text-rose-700 hover:text-rose-800"
                          )
                        )}
                        onClick={(e) => e.stopPropagation()} // Prevent card click
                      >
                        <MoreHorizontal size={14} />
                      </Button>
                    </DropdownMenuTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>More options</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handleDuplicateEntity(entity)}>Duplicate</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
    );
  };

  // Delete Confirmation Dialog component (inside the main component to access state)
  const DeleteConfirmationDialog = () => {
    if (!entityToDelete) return null;

    return (
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this entity?</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to delete <strong>{entityToDelete.name}</strong>.
              {hierarchyLevels.indexOf(entityToDelete.type) < hierarchyLevels.length - 1 && (
                <span> This will also delete all child entities under it.</span>
              )}
              <br /><br />
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDelete}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title="Entity Hierarchy"
          subtitle="Configure your organization's entity hierarchy"
        />

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
              <p className="text-muted-foreground">Loading hierarchy levels...</p>
            </div>
          </div>
        )}

        {error && !isLoading && (
          <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <Info className="h-5 w-5 text-amber-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-amber-800">Note</h3>
                <div className="mt-2 text-sm text-amber-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid gap-6">
          <Card className="shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center">
                <FolderTree className="h-5 w-5 mr-3 text-primary" />
                <CardTitle>Entity Hierarchy Structure</CardTitle>
              </div>
              <CardDescription>
                The hierarchy is structured in levels from highest to lowest as shown below
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2 flex-wrap">
                {hierarchyLevels.map((level, index) => (
                  <div key={level} className="flex items-center">
                    {editingLevelIndex === index ? (
                      <div className="flex items-center">
                        <div className="flex items-center">
                          <Input
                            ref={editLevelInputRef}
                            value={editLevelValue}
                            onChange={(e) => setEditLevelValue(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleSaveEditLevel(index);
                              } else if (e.key === 'Escape') {
                                handleCancelEditLevel();
                              }
                            }}
                            className="h-7 text-sm py-1 px-2 w-32"
                            autoFocus
                            placeholder="Display name"
                          />
                          <div className="flex items-center ml-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 w-7 rounded-full text-green-600 hover:bg-green-50 hover:text-green-700"
                              onClick={() => handleSaveEditLevel(index)}
                            >
                              <Check size={14} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 w-7 rounded-full text-muted-foreground hover:bg-muted/20"
                              onClick={handleCancelEditLevel}
                            >
                              <X size={14} />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-start">
                        <Badge
                          className={cn(getTypeColor(level), "font-normal group relative")}
                          onClick={() => handleStartEditLevel(index)}
                        >
                          {level}
                          <span className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 bg-black/10 rounded cursor-pointer">
                            <Edit size={10} className="text-white" />
                          </span>
                        </Badge>

                      </div>
                    )}
                    {index < hierarchyLevels.length - 1 && (
                      <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground" />
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle>Entity Management</CardTitle>
              <CardDescription>
                Select entities at each level to navigate through the hierarchy. Drag and drop items to reorder them within each column.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
              >
                <ResizablePanelGroup direction="horizontal" className="min-h-[600px] border rounded-md">
                {hierarchyLevels.map((level, index) => (
                  <>
                    <ResizablePanel defaultSize={100 / hierarchyLevels.length} key={level} className="p-0">
                      <div className="h-full flex flex-col">
                        {/* Header */}
                        <div className={cn(
                          "p-3 border-b flex justify-between items-center",
                          getTypeHeaderColor(level)
                        )}>
                          <div className="flex items-center gap-2">
                            <div className={cn(
                              "w-2 h-2 rounded-full",
                              index === 0 && "bg-sky-500",
                              index === 1 && "bg-blue-500",
                              index === 2 && "bg-green-500",
                              index === 3 && "bg-amber-500",
                              index === 4 && "bg-rose-500"
                            )}></div>

                            {editingLevelIndex === index ? (
                              <div className="flex items-center">
                                <Input
                                  ref={editLevelInputRef}
                                  value={editLevelValue}
                                  onChange={(e) => setEditLevelValue(e.target.value)}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                      handleSaveEditLevel(index);
                                    } else if (e.key === 'Escape') {
                                      handleCancelEditLevel();
                                    }
                                  }}
                                  className="h-7 text-sm py-1 px-2 w-32"
                                  autoFocus
                                  onClick={(e) => e.stopPropagation()}
                                />
                                <div className="flex items-center ml-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 w-7 rounded-full text-green-600 hover:bg-green-50 hover:text-green-700"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleSaveEditLevel(index);
                                    }}
                                  >
                                    <Check size={14} />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 w-7 rounded-full text-muted-foreground hover:bg-muted/20"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleCancelEditLevel();
                                    }}
                                  >
                                    <X size={14} />
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="flex items-center gap-2">
                                <h3 className="font-medium text-sm group relative cursor-pointer" onClick={() => handleStartEditLevel(index)}>
                                  {level}
                                  <span className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 bg-black/5 rounded">
                                    <Edit size={10} className="ml-1 text-muted-foreground" />
                                  </span>
                                </h3>
                                <Badge
                                  className={cn(getTypeColor(level), "font-normal")}
                                  variant="outline"
                                >
                                  {getFilteredEntities(level).length}
                                </Badge>
                              </div>
                            )}
                          </div>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-7 w-7">
                                  <Info className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Manage {level.toLowerCase()} entities</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>

                        {/* Unified Search and Add Input */}
                        <div className="p-4 border-b bg-muted/5">
                          <div className="flex space-x-2">
                            <div className="relative flex-1 group">
                              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground group-focus-within:text-primary transition-colors" />
                              <Input
                                placeholder={`Search or add new ${level}...`}
                                className="pl-9 h-9 text-sm focus-visible:ring-1 focus-visible:ring-primary"
                                value={searchQueries[level] || newEntityNames[level]}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  // Update both search and new entity states with the same value
                                  handleSearch(level, value);
                                  setNewEntityNames({...newEntityNames, [level]: value});
                                }}
                                onKeyDown={(e) => {
                                  // Add entity when Enter key is pressed
                                  if (e.key === 'Enter' && newEntityNames[level]) {
                                    handleAddEntity(level);
                                  }
                                  // Don't trigger edit mode when typing in search
                                  e.stopPropagation();
                                }}
                                onClick={(e) => e.stopPropagation()}
                              />
                              {(searchQueries[level] || newEntityNames[level]) && (
                                <div className="absolute right-2.5 top-2.5 text-xs text-muted-foreground">
                                  {getSearchFilteredEntities(level).length} results
                                </div>
                              )}
                            </div>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div>
                                    <Button
                                      size="sm"
                                      variant="default"
                                      onClick={() => handleAddEntity(level)}
                                      className="h-9 w-9 flex-shrink-0 p-0 transition-all"
                                      disabled={!newEntityNames[level] || (index > 0 && !selectedEntities[hierarchyLevels[index - 1]])}
                                    >
                                      <Plus size={16} />
                                    </Button>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Add new {level.toLowerCase()}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>

                        {/* Entity List */}
                        <div className="flex-1 overflow-y-auto p-4">
                          {getSearchFilteredEntities(level).length > 0 ? (
                            <SortableContext
                              items={getSearchFilteredEntities(level).map(entity => entity.id)}
                              strategy={verticalListSortingStrategy}
                            >
                              {getSearchFilteredEntities(level).map((entity) => (
                                <SortableEntityCard
                                  key={entity.id}
                                  entity={entity}
                                  isSelected={selectedEntities[level] === entity.id}
                                />
                              ))}
                            </SortableContext>
                          ) : (
                            <div className="text-center text-muted-foreground py-8 px-3">
                              {searchQueries[level] ? (
                                "No matching entities found"
                              ) : index === 0 ? (
                                "No entities available"
                              ) : (
                                selectedEntities[hierarchyLevels[index - 1]] ?
                                  "No entities under selected parent" :
                                  `Select a ${hierarchyLevels[index - 1]} first`
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </ResizablePanel>
                    {index < hierarchyLevels.length - 1 && (
                      <ResizableHandle withHandle />
                    )}
                  </>
                ))}
              </ResizablePanelGroup>
              <DragOverlay>
                {draggedEntity ? (
                  <EntityCard
                    entity={draggedEntity}
                    isSelected={false}
                    isDragging={true}
                  />
                ) : null}
              </DragOverlay>
            </DndContext>
            </CardContent>
          </Card>
        </div>

        {/* Render the delete confirmation dialog */}
        <DeleteConfirmationDialog />
      </div>
    </MainLayout>
  );
};

export default EntityHierarchy;
