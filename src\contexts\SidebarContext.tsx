import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the context type
type SidebarContextType = {
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  toggleSidebar: () => void;
};

// Create the context with a default value
const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

// Provider props interface
interface SidebarProviderProps {
  children: ReactNode;
}

// Local storage key
const SIDEBAR_STATE_KEY = 'sidebar:collapsed';

// Create a provider component
export const SidebarProvider: React.FC<SidebarProviderProps> = ({ children }) => {
  // Initialize state from localStorage or default to true (collapsed)
  const [isCollapsed, setIsCollapsedState] = useState<boolean>(() => {
    const savedState = localStorage.getItem(SIDEBAR_STATE_KEY);
    return savedState ? JSON.parse(savedState) : true;
  });

  // Update localStorage when state changes
  useEffect(() => {
    localStorage.setItem(SIDEBAR_STATE_KEY, JSON.stringify(isCollapsed));
  }, [isCollapsed]);

  // Function to set the collapsed state
  const setIsCollapsed = (collapsed: boolean) => {
    setIsCollapsedState(collapsed);
  };

  // Function to toggle the sidebar
  const toggleSidebar = () => {
    setIsCollapsedState(prev => !prev);
  };

  // Create the context value object
  const contextValue: SidebarContextType = {
    isCollapsed,
    setIsCollapsed,
    toggleSidebar,
  };

  return (
    <SidebarContext.Provider value={contextValue}>
      {children}
    </SidebarContext.Provider>
  );
};

// Custom hook to use the sidebar context
export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};
