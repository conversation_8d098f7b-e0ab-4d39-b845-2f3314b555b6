
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  AlertTriangle,
  Eye,
  CheckSquare,
  FileCheck,
  AlertOctagon,
  ClipboardCheck
} from "lucide-react";

const Settings = () => {
  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title="Settings"
          subtitle="Manage your account settings and preferences"
        />

        <Card>
          <CardContent className="pt-6">
            <Tabs defaultValue="profile" className="w-full">
              <TabsList className="mb-6">
                <TabsTrigger value="profile">Profile Settings</TabsTrigger>
                <TabsTrigger value="subscription">Subscription Details</TabsTrigger>
                <TabsTrigger value="notifications">Notification Preferences</TabsTrigger>
              </TabsList>

              <TabsContent value="profile">
                <div className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Profile Information</h3>
                    <p className="text-sm text-muted-foreground">
                      Update your account profile information and settings.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="full-name">Full Name</Label>
                        <Input id="full-name" defaultValue="Admin User" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input id="email" type="email" defaultValue="<EMAIL>" />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="job-title">Job Title</Label>
                        <Input id="job-title" defaultValue="System Administrator" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="department">Department</Label>
                        <Input id="department" defaultValue="IT" />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input id="current-password" type="password" />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="new-password">New Password</Label>
                        <Input id="new-password" type="password" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirm-password">Confirm New Password</Label>
                        <Input id="confirm-password" type="password" />
                      </div>
                    </div>
                  </div>

                  <Button>
                    Save Profile
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="subscription">
                <div className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Subscription Details</h3>
                    <p className="text-sm text-muted-foreground">
                      View and manage your subscription information.
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div className="bg-muted/30 p-4 rounded-md border">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">Enterprise Plan</h4>
                          <p className="text-sm text-muted-foreground">All modules included</p>
                        </div>
                        <Badge className="bg-green-500">Active</Badge>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-between border-b pb-2">
                        <span className="text-muted-foreground">Subscription Start Date</span>
                        <span className="font-medium">January 1, 2023</span>
                      </div>

                      <div className="flex justify-between border-b pb-2">
                        <span className="text-muted-foreground">Renewal Date</span>
                        <span className="font-medium">January 1, 2024</span>
                      </div>

                      <div className="flex justify-between border-b pb-2">
                        <span className="text-muted-foreground">Licensed Users</span>
                        <span className="font-medium">500 / 1000</span>
                      </div>

                      <div className="flex justify-between border-b pb-2">
                        <span className="text-muted-foreground">Billing Cycle</span>
                        <span className="font-medium">Annual</span>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="mb-4">
                        <h4 className="font-medium">Active Modules</h4>
                        <p className="text-sm text-muted-foreground">Your subscription includes the following modules</p>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="flex items-center p-3 rounded-md border-l-4 border-l-[#FF6B6B] bg-white shadow-sm hover:shadow transition-all">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#FF6B6B]/10 flex items-center justify-center mr-3">
                            <AlertTriangle className="h-4 w-4 text-[#FF6B6B]" />
                          </div>
                          <span className="text-sm font-medium">Integrated Risk Assessment</span>
                        </div>

                        <div className="flex items-center p-3 rounded-md border-l-4 border-l-[#4ECDC4] bg-white shadow-sm hover:shadow transition-all">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#4ECDC4]/10 flex items-center justify-center mr-3">
                            <Eye className="h-4 w-4 text-[#4ECDC4]" />
                          </div>
                          <span className="text-sm font-medium">Observation Reporting</span>
                        </div>

                        <div className="flex items-center p-3 rounded-md border-l-4 border-l-[#FFD166] bg-white shadow-sm hover:shadow transition-all">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#FFD166]/10 flex items-center justify-center mr-3">
                            <CheckSquare className="h-4 w-4 text-[#FFD166]" />
                          </div>
                          <span className="text-sm font-medium">Operational Tasks</span>
                        </div>

                        <div className="flex items-center p-3 rounded-md border-l-4 border-l-[#06D6A0] bg-white shadow-sm hover:shadow transition-all">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#06D6A0]/10 flex items-center justify-center mr-3">
                            <FileCheck className="h-4 w-4 text-[#06D6A0]" />
                          </div>
                          <span className="text-sm font-medium">ePermit to Work</span>
                        </div>

                        <div className="flex items-center p-3 rounded-md border-l-4 border-l-[#F72585] bg-white shadow-sm hover:shadow transition-all">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#F72585]/10 flex items-center justify-center mr-3">
                            <AlertOctagon className="h-4 w-4 text-[#F72585]" />
                          </div>
                          <span className="text-sm font-medium">Incident Investigation</span>
                        </div>

                        <div className="flex items-center p-3 rounded-md border-l-4 border-l-[#4361EE] bg-white shadow-sm hover:shadow transition-all">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#4361EE]/10 flex items-center justify-center mr-3">
                            <ClipboardCheck className="h-4 w-4 text-[#4361EE]" />
                          </div>
                          <span className="text-sm font-medium">Inspection</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <Button>
                      Contact Support
                    </Button>
                    <Button variant="outline">
                      Download Invoice
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="notifications">
                <div className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Notification Preferences</h3>
                    <p className="text-sm text-muted-foreground">
                      Manage your notification settings and preferences.
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">System Notifications</h4>

                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>System Updates</Label>
                            <p className="text-sm text-muted-foreground">Receive notifications about system updates</p>
                          </div>
                          <Switch defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Security Alerts</Label>
                            <p className="text-sm text-muted-foreground">Get notified about security-related events</p>
                          </div>
                          <Switch defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Maintenance Notifications</Label>
                            <p className="text-sm text-muted-foreground">Receive scheduled maintenance notifications</p>
                          </div>
                          <Switch defaultChecked />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Module Notifications</h4>

                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>User Management</Label>
                            <p className="text-sm text-muted-foreground">Notifications about user changes</p>
                          </div>
                          <Switch defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Risk Assessment Updates</Label>
                            <p className="text-sm text-muted-foreground">Get notified about risk assessment changes</p>
                          </div>
                          <Switch />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Incident Reports</Label>
                            <p className="text-sm text-muted-foreground">Receive notifications about new incidents</p>
                          </div>
                          <Switch defaultChecked />
                        </div>
                      </div>
                    </div>
                  </div>

                  <Button>
                    Save Preferences
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default Settings;
