import { api } from './apiService';

/**
 * API service for making HTTP requests
 */
const API = {
  /**
   * Make a GET request
   * @param url The URL to make the request to
   * @returns The response data
   */
  get: async <T>(url: string): Promise<T> => {
    try {
      return await api.get<T>(url);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Make a POST request
   * @param url The URL to make the request to
   * @param data The data to send with the request
   * @returns The response data
   */
  post: async <T>(url: string, data: any): Promise<T> => {
    try {
      return await api.post<T>(url, data);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Make a PATCH request
   * @param url The URL to make the request to
   * @param data The data to send with the request
   * @returns The response data
   */
  patch: async <T>(url: string, data: any): Promise<T> => {
    try {
      return await api.patch<T>(url, data);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Make a DELETE request
   * @param url The URL to make the request to
   * @returns The response data
   */
  delete: async <T>(url: string): Promise<T> => {
    try {
      return await api.delete<T>(url);
    } catch (error) {
      throw error;
    }
  }
};

export default API;
