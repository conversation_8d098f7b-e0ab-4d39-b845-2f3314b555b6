import { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import axios from 'axios';
import { useAuth } from '@/contexts/AuthContext';
import { useAppSelector } from '@/store/hooks';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import Logo from '@/components/common/Logo';
import { getLoginConfig, LoginConfig, defaultConfig } from '@/services/loginConfigService';
import fallbackConfig from '@/data/loginConfig.json';

// No need for login schema as we're using Cognito OAuth


// Schema for requesting a password reset (step 1)
const requestResetSchema = z.object({
  username: z.string().min(1, 'Username is required'),
});

// Schema for confirming a password reset (step 2)
const confirmResetSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  code: z.string().min(1, 'Verification code is required'),
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Password must be at least 8 characters'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type RequestResetFormValues = z.infer<typeof requestResetSchema>;
type ConfirmResetFormValues = z.infer<typeof confirmResetSchema>;

const Login = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { forgotPassword, resetPassword, handleOAuthCallback } = useAuth();
  const { isAuthenticated } = useAppSelector(state => state.auth);
  const { isLoading: authLoading } = useAuth();

  // Track the password reset flow steps
  const [resetStep, setResetStep] = useState<'none' | 'request' | 'confirm'>('none');
  const [error, setError] = useState<string | null>(null);
  const [loginConfig, setLoginConfig] = useState<LoginConfig | null>(null);
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [configLoading, setConfigLoading] = useState<boolean>(true);
  const [processingOAuth, setProcessingOAuth] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);



  // Check for OAuth callback code in URL - using a ref to prevent multiple calls
  const processedCode = useRef<string | null>(null);

  // Wait for login configuration to be loaded before processing OAuth callback
  useEffect(() => {
    // Don't process the code until login configuration is loaded
    if (configLoading) {
      console.log('Waiting for login configuration to load before processing OAuth callback');
      return;
    }

    const handleOAuthCallbackCode = async () => {
      // Parse the URL search params
      const searchParams = new URLSearchParams(location.search);
      const code = searchParams.get('code');
      const error = searchParams.get('error');
      const errorDescription = searchParams.get('error_description');

      // First, clear the code from the URL to prevent reprocessing on refresh
      // Use history.replaceState to avoid a page reload
      if (code || error) {
        const url = new URL(window.location.href);
        url.searchParams.delete('code');
        url.searchParams.delete('error');
        url.searchParams.delete('error_description');
        window.history.replaceState({}, document.title, url.toString());
      }

      if (error) {
        console.error('OAuth error:', error, errorDescription);
        setError(`Authentication error: ${errorDescription || error}`);
        return;
      }

      // If there's no code or we've already processed this exact code, don't proceed
      if (!code || processedCode.current === code) {
        return;
      }

      // Make sure we have login configuration
      if (!loginConfig) {
        console.error('Login configuration not available, using default config');
        // Set the default config if not already set
        setLoginConfig(defaultConfig);
      }

      // Store the code we're processing to prevent duplicate processing
      processedCode.current = code;

      console.log('OAuth callback code found in URL:', code);
      setProcessingOAuth(true);
      setError(null);

      try {
        // Use the AuthContext method to handle the OAuth callback
        console.log('Using AuthContext to handle OAuth callback');
        const user = await handleOAuthCallback(code);

        if (user) {
          console.log('OAuth authentication successful, user:', user);

          // Check if we have a location to redirect to
          const from = location.state?.from?.pathname || '/';
          console.log('Redirecting after login to:', from);

          // Navigate to the saved location or home page after successful login
          navigate(from, { replace: true });
        } else {
          setError('Authentication failed. Please try again.');
          // Reset the processed code if authentication failed
          processedCode.current = null;
        }
      } catch (error) {
        console.error('OAuth callback error:', error);
        setError('An error occurred during authentication. Please try again.');
        // Reset the processed code if there was an error
        processedCode.current = null;
      } finally {
        setProcessingOAuth(false);
      }
    };

    handleOAuthCallbackCode();
  }, [location.search, location.state, handleOAuthCallback, navigate, configLoading, loginConfig]);

  // If already authenticated, redirect to home
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if we're already authenticated
        if (isAuthenticated) {
          // Check if we have a manual logout flag
          const manualLogout = sessionStorage.getItem('manual_logout');

          if (manualLogout === 'true') {
            // Clear the flag
            console.log('Manual logout detected, not redirecting to home');
            sessionStorage.removeItem('manual_logout');
          } else {
            console.log('Already authenticated, redirecting to home');
            navigate('/', { replace: true });
          }
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
      }
    };

    checkAuth();
  }, [isAuthenticated, navigate]);

  // Function to fetch presigned URL for logo
  const fetchLogoPresignedUrl = async (filename: string): Promise<string> => {
    try {
      console.log('Fetching presigned URL for logo:', filename);

      // Use CORS proxy if provided
      const CORS_PROXY_URL = import.meta.env.VITE_CORS_PROXY_URL || '';

      // Construct the correct URL format for the presigned URL endpoint
      const apiUrl = CORS_PROXY_URL
        ? `${CORS_PROXY_URL}https://admin.client-api.acuizen.com/files/${encodeURIComponent(filename)}/presigned-url`
        : `https://admin.client-api.acuizen.com/files/${encodeURIComponent(filename)}/presigned-url`;

      console.log('Requesting presigned URL from:', apiUrl);

      // Get access token from localStorage
      const accessToken = localStorage.getItem('access_token');

      const response = await axios.get(apiUrl, {
        timeout: 5000, // 5 second timeout
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {})
        }
      });

      console.log('Presigned URL response:', response.data);

      // The response might directly be the URL or have a url property
      const presignedUrl = typeof response.data === 'string' ? response.data : response.data.url;
      console.log('Extracted presigned URL:', presignedUrl);

      return presignedUrl || '';
    } catch (error) {
      console.error('Failed to fetch presigned URL for logo:', error);
      if (axios.isAxiosError(error)) {
        console.error('Axios error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message
        });
      }
      return '';
    }
  };

  // Fetch login configuration
  useEffect(() => {
    const fetchConfig = async () => {
      setConfigLoading(true);
      try {
        const config = await getLoginConfig();
        console.log('Login configuration loaded:', config);
        setLoginConfig(config);

        // Set logo URL if available
        if (config.LOGO) {
          console.log('Logo filename from config:', config.LOGO);

          // Check if the logo URL is absolute or relative
          if (config.LOGO.startsWith('http')) {
            console.log('Using absolute URL for logo');
            setLogoUrl(config.LOGO);
          } else {
            console.log('Fetching presigned URL for logo');
            // Fetch presigned URL for the logo
            const presignedUrl = await fetchLogoPresignedUrl(config.LOGO);
            if (presignedUrl) {
              console.log('Using presigned URL for logo:', presignedUrl);

              // Directly use the presigned URL instead of converting to blob
              console.log('Using direct presigned URL for logo:', presignedUrl);
              setLogoUrl(presignedUrl);
            } else {
              console.log('Using fallback direct URL for logo');
              // Fallback to direct URL if presigned URL fails
              const directUrl = `https://admin.client-api.acuizen.com/files/${config.LOGO}`;

              // Directly use the direct URL instead of converting to blob
              console.log('Using direct URL for fallback logo:', directUrl);
              setLogoUrl(directUrl);
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch login configuration:', error);
        // Use fallback configuration
        console.log('Using fallback configuration:', fallbackConfig);
        setLoginConfig(defaultConfig);

        // Set logo URL if available in fallback
        if (fallbackConfig.LOGO) {
          console.log('Logo filename from fallback config:', fallbackConfig.LOGO);

          // Check if the logo URL is absolute or relative
          if (fallbackConfig.LOGO.startsWith('http')) {
            console.log('Using absolute URL for fallback logo');
            setLogoUrl(fallbackConfig.LOGO);
          } else {
            console.log('Fetching presigned URL for fallback logo');
            // Fetch presigned URL for the logo
            try {
              const presignedUrl = await fetchLogoPresignedUrl(fallbackConfig.LOGO);
              if (presignedUrl) {
                console.log('Using presigned URL for fallback logo:', presignedUrl);

                // Directly use the presigned URL instead of converting to blob
                console.log('Using direct presigned URL for fallback logo:', presignedUrl);
                setLogoUrl(presignedUrl);
              } else {
                console.log('Using fallback direct URL for logo');
                // Fallback to direct URL if presigned URL fails
                const directUrl = `https://admin.client-api.acuizen.com/files/${fallbackConfig.LOGO}`;

                // Directly use the direct URL instead of converting to blob
                console.log('Using direct URL for fallback logo:', directUrl);
                setLogoUrl(directUrl);
              }
            } catch (error) {
              console.log('Error fetching presigned URL, using direct URL as final fallback');
              // Final fallback to direct URL
              const directUrl = `https://admin.client-api.acuizen.com/files/${fallbackConfig.LOGO}`;
              setLogoUrl(directUrl);
            }
          }
        }
      } finally {
        setConfigLoading(false);
      }
    };

    fetchConfig();
  }, []);

  // Function to handle Cognito OAuth login
  const handleCognitoLogin = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setError(null);

    if (!loginConfig) {
      setError('Login configuration not available. Please try again later.');
      return;
    }

    try {
      setIsLoading(true);

      // Construct the OAuth URL with the correct redirect URI
      const redirectUri = window.location.origin + '/login';
      const oauthUrl = `${loginConfig.COGNITO_ADMIN_DOMAIN}/oauth2/authorize?client_id=${loginConfig.COGNITO_ADMIN_APP_CLIENT_ID}&response_type=code&scope=email+openid+phone&redirect_uri=${encodeURIComponent(redirectUri)}`;

      console.log('Redirecting to Cognito OAuth URL:', oauthUrl);
      console.log('Redirect URI:', redirectUri);

      // Redirect to Cognito login page
      window.location.href = oauthUrl;
    } catch (error) {
      console.error('Error initiating Cognito login:', error);
      setError('Failed to initiate login. Please try again.');
      setIsLoading(false);
    }
  };



  // Request password reset form (step 1)
  const requestResetForm = useForm<RequestResetFormValues>({
    resolver: zodResolver(requestResetSchema),
    defaultValues: {
      username: '',
    },
  });

  // Confirm password reset form (step 2)
  const confirmResetForm = useForm<ConfirmResetFormValues>({
    resolver: zodResolver(confirmResetSchema),
    defaultValues: {
      username: '',
      code: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // We're now using Cognito OAuth for login, so no need for form submission



  // Handle request password reset form submission (step 1)
  const onRequestResetSubmit = async (values: RequestResetFormValues) => {
    setError(null);
    try {
      // Call the forgotPassword function from AuthContext
      const success = await forgotPassword(values.username);
      if (success) {
        // Move to the confirm reset step
        setResetStep('confirm');
        // Pre-fill the username in the confirm form
        confirmResetForm.setValue('username', values.username);
      }
    } catch (error) {
      console.error('Error requesting password reset:', error);
      setError('Failed to send verification code. Please try again.');
    }
  };

  // Handle confirm password reset form submission (step 2)
  const onConfirmResetSubmit = async (values: ConfirmResetFormValues) => {
    setError(null);
    try {
      const success = await resetPassword(values.username, values.code, values.newPassword);
      if (success) {
        // Return to login form
        setResetStep('none');
      }
    } catch (error) {
      console.error('Error confirming password reset:', error);
      setError('Failed to reset password. Please check your code and try again.');
    }
  };

  return (
    <>
      {/* Google Fonts */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link
        href="https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;600;700&family=Lato:wght@300;400;500;600&display=swap"
        rel="stylesheet"
      />

      <div className="flex min-h-screen bg-gray-50 relative">
        {/* Top Center Text - Positioned over left side image */}
        <div className="absolute top-8 left-1/4 transform -translate-x-1/2 z-10 hidden md:block">
          <h1
            className="text-4xl md:text-5xl font-bold text-white text-center drop-shadow-lg"
            style={{ fontFamily: 'Raleway, sans-serif' }}
          >
            AcuiZen WorkHub
          </h1>
        </div>

        {/* Bottom Center Text - Positioned over left side image */}
        <div className="absolute bottom-8 left-1/4 transform -translate-x-1/2 z-10 hidden md:block">
          <p
            className="text-base md:text-lg text-white text-center drop-shadow-lg whitespace-nowrap px-4"
            style={{ fontFamily: 'Lato, sans-serif' }}
          >
            Empower individuals with knowledge, insights and digital tools to get work done
          </p>
        </div>

        {/* Left side - Background image */}
        <div className="hidden md:block md:w-1/2 relative overflow-hidden">
          <img
            src="/locales/images/img.jpg"
            alt="Engineers working together"
            className="absolute inset-0 w-full h-full object-cover"
          />
          {/* Optional overlay for better text readability if needed */}
          <div className="absolute inset-0 bg-primary/20"></div>
        </div>

      {/* Right side - Login form */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-8 bg-white">
        <div className="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
          {/* Logo - Visible on all screen sizes */}
          <div className="mb-12 flex justify-center">
            {configLoading ? (
              <div className="h-20 w-20 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : logoUrl ? (
              <img
                src={logoUrl}
                alt="Company Logo"
                className="h-20 object-contain"
                onError={(e) => {
                  console.error('Failed to load logo image:', e);
                  setLogoUrl(''); // Fallback to default logo if image fails to load
                }}
                loading="eager"
              />
            ) : (
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">
                  Acui<span className="text-red-600">Zen</span>
                </div>
              </div>
            )}
          </div>

          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-3">
              Welcome
            </h1>
            <p className="text-gray-600">
              Sign in to access your personalized dashboard
            </p>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Show the appropriate form based on the reset step */}
          {resetStep === 'none' && (
            <div className="space-y-6">
              <Button
                onClick={handleCognitoLogin}
                className="w-full h-12 text-base font-medium bg-primary hover:bg-primary/90 text-white rounded-lg shadow-sm"
                disabled={isLoading || processingOAuth || authLoading}
              >
                {isLoading || processingOAuth || authLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    {processingOAuth ? t('auth.authenticating') : t('auth.loggingIn')}
                  </>
                ) : (
                  <>
                    Get Started
                    <span className="ml-2">✨</span>
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Step 1: Request password reset */}
          {resetStep === 'request' && (
            <Form {...requestResetForm}>
              <form onSubmit={requestResetForm.handleSubmit(onRequestResetSubmit)} className="space-y-6">
                <FormField
                  control={requestResetForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">{t('auth.username')}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('auth.usernamePlaceholder')}
                          className="h-12 rounded-lg border-gray-300 focus:border-primary focus:ring-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full h-12 text-base font-medium bg-primary hover:bg-primary/90 text-white rounded-lg shadow-sm"
                  disabled={authLoading}
                >
                  {authLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      {t('auth.sending')}
                    </>
                  ) : (
                    t('auth.sendResetCode')
                  )}
                </Button>
              </form>
            </Form>
          )}

          {/* Step 2: Confirm password reset */}
          {resetStep === 'confirm' && (
            <Form {...confirmResetForm}>
              <form onSubmit={confirmResetForm.handleSubmit(onConfirmResetSubmit)} className="space-y-6">
                <FormField
                  control={confirmResetForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">{t('auth.username')}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('auth.usernamePlaceholder')}
                          className="h-12 rounded-lg border-gray-300 focus:border-primary focus:ring-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={confirmResetForm.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">{t('auth.verificationCode')}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('auth.verificationCodePlaceholder')}
                          className="h-12 rounded-lg border-gray-300 focus:border-primary focus:ring-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={confirmResetForm.control}
                  name="newPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">{t('auth.newPassword')}</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder={t('auth.newPasswordPlaceholder')}
                          className="h-12 rounded-lg border-gray-300 focus:border-primary focus:ring-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={confirmResetForm.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">{t('auth.confirmPassword')}</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder={t('auth.confirmPasswordPlaceholder')}
                          className="h-12 rounded-lg border-gray-300 focus:border-primary focus:ring-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                      <p className="text-xs text-gray-500 mt-1">
                        {t('auth.passwordRequirements')}
                      </p>
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full h-12 text-base font-medium bg-primary hover:bg-primary/90 text-white rounded-lg shadow-sm"
                  disabled={authLoading}
                >
                  {authLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      {t('auth.resetting')}
                    </>
                  ) : (
                    t('auth.resetPassword')
                  )}
                </Button>
              </form>
            </Form>
          )}
          <div className="mt-8 flex justify-center">
            {resetStep === 'request' && (
              /* Show "Back to Login" button when on request reset form */
              <Button
                variant="link"
                onClick={() => {
                  setResetStep('none');
                }}
                className="text-sm text-gray-500 hover:text-primary"
              >
                {t('auth.backToLogin')}
              </Button>
            )}

            {resetStep === 'confirm' && (
              /* Show "Back" button when on confirm reset form */
              <Button
                variant="link"
                onClick={() => {
                  setResetStep('request');
                }}
                className="text-sm text-gray-500 hover:text-primary"
              >
                {t('auth.back')}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default Login;
