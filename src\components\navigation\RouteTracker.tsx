import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setCurrentPath } from '@/store/slices/navigationSlice';

interface RouteTrackerProps {
  children: React.ReactNode;
}

const RouteTracker: React.FC<RouteTrackerProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { currentPath } = useAppSelector(state => state.navigation);
  const { isAuthenticated } = useAppSelector(state => state.auth);

  // Update Redux store when location changes
  useEffect(() => {
    // Always update the Redux store with the current location
    console.log(`Updating current path in Redux: ${location.pathname}`);
    dispatch(setCurrentPath(location.pathname));
  }, [location.pathname, dispatch]);

  // We're removing the automatic redirect to stored path
  // This was causing issues with the home page navigation

  return <>{children}</>;
};

export default RouteTracker;
