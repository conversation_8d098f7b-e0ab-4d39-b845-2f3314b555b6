/**
 * Axios Instance Configuration
 * 
 * Sets up a configured Axios instance with interceptors for authentication and error handling.
 */

import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { toast } from '@/hooks/use-toast';
import tokenService from './tokenService';
import { getBaseUrl, AUTH_ENDPOINTS } from './endpoints';

// Create axios instance with base configuration
const axiosInstance: AxiosInstance = axios.create({
  baseURL: getBaseUrl(),
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Flag to prevent multiple refresh token requests
let isRefreshing = false;

// Store pending requests that should be retried after token refresh
let failedQueue: Array<{
  resolve: (value?: unknown) => void;
  reject: (reason?: any) => void;
  config: InternalAxiosRequestConfig;
}> = [];

// Process the queue of failed requests
const processQueue = (error: AxiosError | null, token: string | null = null) => {
  failedQueue.forEach(request => {
    if (error) {
      request.reject(error);
    } else {
      // Add the new token to the request header
      if (token) {
        request.config.headers.Authorization = `Bearer ${token}`;
      }
      request.resolve(axiosInstance(request.config));
    }
  });
  
  // Reset the queue
  failedQueue = [];
};

// Request interceptor to add auth token to requests
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = tokenService.getAccessToken();
    
    // If token exists, add it to the request header
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh and errors
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig;
    
    // If there's no config, we can't retry the request
    if (!originalRequest) {
      return Promise.reject(error);
    }
    
    // Prevent infinite loops - don't retry refresh token requests
    const isRefreshTokenRequest = originalRequest.url?.includes(AUTH_ENDPOINTS.REFRESH_TOKEN);
    
    // Handle 401 Unauthorized errors (expired token)
    if (
      error.response?.status === 401 && 
      !originalRequest._retry && 
      !isRefreshTokenRequest
    ) {
      originalRequest._retry = true;
      
      // If already refreshing, add request to queue
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject, config: originalRequest });
        });
      }
      
      isRefreshing = true;
      
      try {
        const refreshToken = tokenService.getRefreshToken();
        
        // If no refresh token, logout and reject
        if (!refreshToken) {
          handleAuthError();
          return Promise.reject(error);
        }
        
        // Call refresh token endpoint
        const response = await axios.post(
          `${getBaseUrl()}${AUTH_ENDPOINTS.REFRESH_TOKEN}`,
          { refreshToken }
        );
        
        // Get new tokens
        const { accessToken, refreshToken: newRefreshToken } = response.data;
        
        // Update tokens in storage
        tokenService.setTokens(accessToken, newRefreshToken);
        
        // Update Authorization header for the original request
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        
        // Process any requests in the queue
        processQueue(null, accessToken);
        
        // Retry the original request
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        // If refresh token fails, logout user
        processQueue(refreshError as AxiosError);
        handleAuthError();
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }
    
    // Handle other error status codes
    handleErrorResponse(error);
    
    return Promise.reject(error);
  }
);

/**
 * Handle authentication errors (logout user)
 */
const handleAuthError = () => {
  // Clear tokens
  tokenService.clearTokens();
  
  // Show error message
  toast({
    title: "Session Expired",
    description: "Your session has expired. Please log in again.",
    variant: "destructive",
  });
  
  // Redirect to login page
  setTimeout(() => {
    window.location.href = '/login';
  }, 1500);
};

/**
 * Handle error responses with appropriate messages
 */
const handleErrorResponse = (error: AxiosError) => {
  const status = error.response?.status;
  
  // Default error message
  let errorMessage = "An unexpected error occurred. Please try again.";
  
  // Customize message based on status code
  switch (status) {
    case 400:
      errorMessage = "Invalid request. Please check your data and try again.";
      break;
    case 401:
      // 401 is handled separately in the interceptor
      errorMessage = "Unauthorized. Please log in again.";
      break;
    case 403:
      errorMessage = "You don't have permission to access this resource.";
      break;
    case 404:
      errorMessage = "The requested resource was not found.";
      break;
    case 422:
      errorMessage = "Validation error. Please check your input.";
      break;
    case 500:
      errorMessage = "Server error. Please try again later.";
      break;
    case 503:
      errorMessage = "Service unavailable. Please try again later.";
      break;
  }
  
  // Get more specific error message from response if available
  const responseData = error.response?.data as any;
  if (responseData?.message) {
    errorMessage = responseData.message;
  }
  
  // Show toast notification for errors (except 401 which is handled separately)
  if (status !== 401) {
    toast({
      title: "Error",
      description: errorMessage,
      variant: "destructive",
    });
  }
};

export default axiosInstance;
