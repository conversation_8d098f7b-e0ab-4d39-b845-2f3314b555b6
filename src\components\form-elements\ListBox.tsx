import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Pencil, Trash2, Plus, Check, X, Info } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogFooter,
} from '@/components/ui/dialog';

interface ListItem {
  id: string;
  name: string;
  severityLevel?: string;
  [key: string]: any;
}

interface ListBoxProps {
  title: string;
  lists: ListItem[];
  selected: boolean;
  selectedItem: ListItem;
  mode: string;
  handleSelect: (id: string) => void;
  onHandleCreateItem: (value: string) => void;
  handleEditItem: (id: string, name: string) => void;
  handleDeleteItem: (mode: string, id: string) => void;
  handleAddDescription?: (id: string, data: any) => void;
}

const ListBox: React.FC<ListBoxProps> = ({
  title,
  lists,
  selected,
  selectedItem,
  mode,
  handleSelect,
  onHandleCreateItem,
  handleEditItem,
  handleDeleteItem,
  handleAddDescription
}) => {
  const [newItemName, setNewItemName] = useState('');
  const [editItemId, setEditItemId] = useState<string | null>(null);
  const [editItemName, setEditItemName] = useState('');
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<string | null>(null);
  const [isDescriptionDialogOpen, setIsDescriptionDialogOpen] = useState(false);
  const [descriptionItem, setDescriptionItem] = useState<ListItem | null>(null);
  const [severityLevel, setSeverityLevel] = useState('');

  const handleCreateItem = () => {
    if (newItemName.trim()) {
      onHandleCreateItem(newItemName.trim());
      setNewItemName('');
    } else {
      toast({
        title: "Error",
        description: "Item name cannot be empty",
        variant: "destructive",
      });
    }
  };

  const startEdit = (item: ListItem) => {
    setEditItemId(item.id);
    setEditItemName(item.name);
  };

  const cancelEdit = () => {
    setEditItemId(null);
    setEditItemName('');
  };

  const saveEdit = (id: string) => {
    if (editItemName.trim()) {
      handleEditItem(id, editItemName.trim());
      setEditItemId(null);
      setEditItemName('');
    } else {
      toast({
        title: "Error",
        description: "Item name cannot be empty",
        variant: "destructive",
      });
    }
  };

  const confirmDelete = (id: string) => {
    setItemToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  const executeDelete = () => {
    if (itemToDelete) {
      handleDeleteItem(mode, itemToDelete);
      setIsDeleteDialogOpen(false);
      setItemToDelete(null);
    }
  };

  const openDescriptionDialog = (item: ListItem) => {
    setDescriptionItem(item);
    setSeverityLevel(item.severityLevel || '');
    setIsDescriptionDialogOpen(true);
  };

  const saveDescription = () => {
    if (descriptionItem) {
      handleAddDescription?.(descriptionItem.id, { severityLevel });
      setIsDescriptionDialogOpen(false);
      setDescriptionItem(null);
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-2">
        <h4 className="text-lg font-medium">{title}</h4>
      </div>
      
      <div className="mb-4 flex">
        <Input
          type="text"
          value={newItemName}
          onChange={(e) => setNewItemName(e.target.value)}
          placeholder="Add new item"
          className="mr-2"
          disabled={!selected}
        />
        <Button 
          onClick={handleCreateItem} 
          disabled={!selected}
          size="sm"
        >
          <Plus className="h-4 w-4 mr-1" /> Add
        </Button>
      </div>
      
      <div className="border rounded-md overflow-hidden">
        <div className="max-h-[300px] overflow-y-auto">
          {lists.length > 0 ? (
            <ul className="divide-y">
              {lists.map((item) => (
                <li 
                  key={item.id} 
                  className={`p-2 hover:bg-gray-50 ${selectedItem.id === item.id ? 'bg-blue-50' : ''}`}
                >
                  <div className="flex items-center justify-between">
                    {editItemId === item.id ? (
                      <div className="flex items-center flex-grow mr-2">
                        <Input
                          type="text"
                          value={editItemName}
                          onChange={(e) => setEditItemName(e.target.value)}
                          className="mr-2"
                        />
                        <div className="flex">
                          <Button 
                            onClick={() => saveEdit(item.id)} 
                            size="sm" 
                            variant="ghost"
                            className="mr-1"
                          >
                            <Check className="h-4 w-4 text-green-500" />
                          </Button>
                          <Button 
                            onClick={cancelEdit} 
                            size="sm" 
                            variant="ghost"
                          >
                            <X className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div 
                          className="flex-grow cursor-pointer"
                          onClick={() => handleSelect(item.id)}
                        >
                          {item.name}
                        </div>
                        <div className="flex">
                          {handleAddDescription && (
                            <Button 
                              onClick={() => openDescriptionDialog(item)} 
                              size="sm" 
                              variant="ghost"
                              className="mr-1"
                            >
                              <Info className="h-4 w-4 text-blue-500" />
                            </Button>
                          )}
                          <Button 
                            onClick={() => startEdit(item)} 
                            size="sm" 
                            variant="ghost"
                            className="mr-1"
                          >
                            <Pencil className="h-4 w-4 text-amber-500" />
                          </Button>
                          <Button 
                            onClick={() => confirmDelete(item.id)} 
                            size="sm" 
                            variant="ghost"
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-4 text-center text-gray-500">
              {selected ? "No items available" : "Please select a parent item first"}
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete this item? This action cannot be undone.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={executeDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Description Dialog */}
      {handleAddDescription && (
        <Dialog open={isDescriptionDialogOpen} onOpenChange={setIsDescriptionDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Description</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <label className="block text-sm font-medium mb-2">Severity Level</label>
              <Input
                type="text"
                value={severityLevel}
                onChange={(e) => setSeverityLevel(e.target.value)}
                placeholder="Enter severity level"
              />
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDescriptionDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={saveDescription}>
                Save
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default ListBox;
