/**
 * API Module Index
 * 
 * Exports all API-related modules for easy importing.
 */

import apiClient from './apiClient';
import axiosInstance from './axiosInstance';
import endpoints from './endpoints';
import tokenService from './tokenService';

// Re-export individual modules
export { apiClient, axiosInstance, endpoints, tokenService };

// Export types
export type { ApiResponse } from './apiClient';

// Default export for convenience
export default {
  client: apiClient,
  axios: axiosInstance,
  endpoints,
  tokenService,
};
