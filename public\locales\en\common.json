{"app": {"title": "AcuiZen WorkHub", "subtitle": "Administration and Configuration"}, "navigation": {"home": "Home", "modules": "Subscribed Modules", "generalConfig": "General Configuration", "entityHierarchy": "Entity Hierarchy", "workAreas": "Work Areas", "workActivities": "Work Activities", "languages": "Languages", "userManagement": "User Management", "roleAssignment": "Role Assignment", "activityLog": "Activity Log", "changeRequest": "Change Request", "settings": "Settings", "logout": "Logout"}, "common": {"search": "Search", "filter": "Filter", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "add": "Add", "create": "Create", "update": "Update", "view": "View", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "submit": "Submit", "reset": "Reset", "clear": "Clear", "close": "Close", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "select": "Select", "apply": "Apply", "actions": "Actions", "status": "Status", "details": "Details", "name": "Name", "description": "Description", "date": "Date", "time": "Time", "user": "User", "module": "<PERSON><PERSON><PERSON>", "id": "ID", "type": "Type", "category": "Category", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "default": "<PERSON><PERSON><PERSON>", "required": "Required", "optional": "Optional", "all": "All", "none": "None", "yes": "Yes", "no": "No", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "loading": "Loading", "noData": "No data available", "noResults": "No results found", "tryAgain": "Try again", "saveChanges": "Save Changes", "discardChanges": "Discard Changes", "confirmDelete": "Are you sure you want to delete this item?", "confirmAction": "Are you sure you want to perform this action?", "searchPlaceholder": "Search...", "filterBy": "Filter by {{field}}", "sortBy": "Sort by {{field}}", "itemsPerPage": "Items per page", "showingItems": "Showing {{start}} to {{end}} of {{total}} items", "pageOf": "Page {{current}} of {{total}}"}, "activityLog": {"title": "Activity Log", "subtitle": "View and monitor system activity", "timestamp": "Timestamp", "user": "User", "action": "Action", "module": "<PERSON><PERSON><PERSON>", "details": "Details", "id": "ID", "searchPlaceholder": "Search activity logs...", "filterByModule": "Filter by module", "fromDate": "From Date", "toDate": "To Date", "noLogsFound": "No activity logs found. Try a different search term or filter."}, "changeRequest": {"title": "Title", "subtitle": "Submit and track change requests through workflow stages", "submitNew": "Submit New Change Request", "id": "Request ID", "description": "Description", "reason": "Reason/Justification", "requestedBy": "Requested By", "changeOwner": "Change Owner", "dateRequested": "Date Requested", "review": "Review", "approval": "Approval", "implementation": "Implementation", "documentation": "Documentation", "submit": "Submit Request", "titlePlaceholder": "Enter change request title...", "descriptionPlaceholder": "Describe the change in detail...", "reasonPlaceholder": "Explain the reason or justification for this change...", "requestedByPlaceholder": "Enter your name...", "changeOwnerPlaceholder": "Enter responsible party or team...", "searchPlaceholder": "Search change requests...", "filterByStatus": "Filter by status", "pending": "Pending", "inReview": "In Review", "approved": "Approved", "denied": "Denied", "inProgress": "In Progress", "completed": "Completed", "failed": "Failed", "noRequestsFound": "No change requests found. Try a different search term or filter."}, "languages": {"title": "Languages", "subtitle": "Configure available languages for the platform", "language": "Language", "code": "Code", "status": "Status", "default": "<PERSON><PERSON><PERSON>", "settings": "Language Settings", "availableLanguages": "Available Languages", "availableLanguagesDesc": "Enable or disable languages that will be available for users in the platform. Disabled languages won't be shown in language selection menus.", "defaultLanguage": "Default Language", "defaultLanguageDesc": "Set the default language for new users and system communications. The default language must be enabled.", "note": "Note", "noteDesc": "Language changes will apply to new users immediately. Existing users can change their language preference in their profile settings.", "cannotDisableDefault": "Cannot disable default language", "changeDefaultFirst": "Please change the default language before disabling this one.", "cannotSetAsDefault": "Cannot set as default", "enableFirst": "Please enable the language before setting it as default.", "settingsSaved": "Setting<PERSON> saved", "settingsSavedDesc": "Language settings have been updated successfully."}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "usernamePlaceholder": "Enter your username", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot password?", "resetPassword": "Reset Password", "sendResetCode": "Send Reset Code", "verificationCode": "Verification Code", "verificationCodePlaceholder": "Enter verification code", "newPassword": "New Password", "newPasswordPlaceholder": "Enter new password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "backToLogin": "Back to Login", "welcomeBack": "Welcome Back", "loginDescription": "Sign in to your account to continue", "loggingIn": "Logging in...", "sending": "Sending...", "resetting": "Resetting...", "sessionExpired": "Your session has expired. Please log in again."}, "errors": {"somethingWentWrong": "Something went wrong", "tryAgainLater": "Please try again later", "pageNotFound": "Page not found", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "serverError": "Server error", "networkError": "Network error", "connectionLost": "Connection lost", "invalidInput": "Invalid input", "requiredField": "This field is required", "invalidFormat": "Invalid format", "alreadyExists": "Already exists", "notFound": "Not found"}}