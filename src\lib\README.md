# AZ Brand Color Palette

This document provides information about the AZ brand color palette implementation in the Admin Portal application.

## Primary Colors

| Color | Hex Code | RGB | CMYK | Description |
|-------|----------|-----|------|-------------|
| ![Blue](https://via.placeholder.com/30/005284/FFFFFF?text=+) | `#005284` | rgb(0, 82, 132) | cmyk(100, 38, 0, 48) | AZ Blue |
| ![Red](https://via.placeholder.com/30/BE1F24/FFFFFF?text=+) | `#BE1F24` | rgb(190, 31, 36) | cmyk(0, 84, 81, 25) | AZ Red |

## Color Variations

### Blue Palette

| Shade | Hex Code | Description |
|-------|----------|-------------|
| Light | `#0F6399` | Lighter version of AZ Blue |
| Default | `#005284` | AZ Blue |
| Dark | `#004270` | Darker version of AZ Blue |

### Red Palette

| Shade | Hex Code | Description |
|-------|----------|-------------|
| Light | `#D43339` | Lighter version of AZ Red |
| Default | `#BE1F24` | AZ Red |
| Dark | `#A51A1F` | Darker version of AZ Red |

## Implementation

The color palette is implemented in the following files:

1. `src/lib/colors.ts` - Defines the color constants and utility functions
2. `src/index.css` - Sets CSS variables for the application
3. `tailwind.config.ts` - Configures Tailwind CSS to use the brand colors
4. `src/components/common/Logo.tsx` - Implements the logo with brand colors
5. `public/favicon.svg` - Creates a favicon using the brand colors

## Usage

### In Components

```tsx
// Using Tailwind classes
<div className="text-brand-blue">Blue text</div>
<div className="text-brand-red">Red text</div>
<div className="bg-brand-blue">Blue background</div>

// Using CSS variables
<div style={{ color: 'hsl(var(--primary))' }}>Primary color</div>

// Using direct color values
import { BRAND_COLORS } from '@/lib/colors';
<div style={{ color: BRAND_COLORS.blue.DEFAULT }}>Blue text</div>
```

### In CSS

```css
.my-element {
  color: hsl(var(--primary));
  background-color: hsl(var(--accent));
  border-color: hsl(var(--primary));
}
```

## Module-Specific Colors

The application uses variations of the brand colors for different modules:

| Module | Color | Hex Code |
|--------|-------|----------|
| Risk | AZ Red | `#BE1F24` |
| Observation | AZ Blue 400 | `#4C9FC7` |
| Tasks | AZ Blue Light | `#0F6399` |
| Permit | AZ Blue Dark | `#004270` |
| Incident | AZ Red Dark | `#A51A1F` |
| Knowledge | AZ Blue 300 | `#6BB0D0` |
| Inspection | AZ Red Light | `#D43339` |
