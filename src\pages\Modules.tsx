
import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertTriangle,
  Eye,
  CheckSquare,
  FileCheck,
  AlertOctagon,
  BookOpen,
  ClipboardCheck,
  Loader2,
  Settings
} from "lucide-react";
import { getModules, Module, fetchModuleDropdowns, Dropdown } from "@/services/moduleService";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { toast } from "@/components/ui/use-toast";
import DynamicTab from "@/components/DynamicTab";

interface ModuleCardProps {
  id: string;
  name: string;
  description: string;
  status: "active" | "inactive";
  type: "risk" | "observation" | "tasks" | "permit" | "incident" | "knowledge" | "inspection";
  color?: string; // Optional color property from API
  onConfigureClick: (moduleId: string, moduleName: string) => void;
}

const ModuleCard = ({ id, name, description, status, type, color, onConfigureClick }: ModuleCardProps) => {
  // Define module colors with vibrant, professional colors
  const moduleColors = {
    risk: "#E53935",        // Red
    observation: "#1E88E5", // Blue
    tasks: "#43A047",       // Green
    permit: "#8E24AA",      // Purple
    incident: "#FB8C00",    // Orange
    knowledge: "#00ACC1",   // Teal
    inspection: "#6D4C41"   // Brown
  };

  // Get the color for this module - use the color from API if available, otherwise use the default color
  const borderColor = color || moduleColors[type];

  // Function to get the appropriate icon based on module type
  const getModuleIcon = () => {
    const iconColor = { color: borderColor };
    const bgStyle = { backgroundColor: `${borderColor}20` }; // 20 is hex for 12% opacity

    switch (type) {
      case "risk":
        return (
          <div className="h-8 w-8 rounded-full flex items-center justify-center" style={bgStyle}>
            <AlertTriangle className="h-5 w-5" style={iconColor} />
          </div>
        );
      case "observation":
        return (
          <div className="h-8 w-8 rounded-full flex items-center justify-center" style={bgStyle}>
            <Eye className="h-5 w-5" style={iconColor} />
          </div>
        );
      case "tasks":
        return (
          <div className="h-8 w-8 rounded-full flex items-center justify-center" style={bgStyle}>
            <CheckSquare className="h-5 w-5" style={iconColor} />
          </div>
        );
      case "permit":
        return (
          <div className="h-8 w-8 rounded-full flex items-center justify-center" style={bgStyle}>
            <FileCheck className="h-5 w-5" style={iconColor} />
          </div>
        );
      case "incident":
        return (
          <div className="h-8 w-8 rounded-full flex items-center justify-center" style={bgStyle}>
            <AlertOctagon className="h-5 w-5" style={iconColor} />
          </div>
        );
      case "knowledge":
        return (
          <div className="h-8 w-8 rounded-full flex items-center justify-center" style={bgStyle}>
            <BookOpen className="h-5 w-5" style={iconColor} />
          </div>
        );
      case "inspection":
        return (
          <div className="h-8 w-8 rounded-full flex items-center justify-center" style={bgStyle}>
            <ClipboardCheck className="h-5 w-5" style={iconColor} />
          </div>
        );
      default:
        return null;
    }
  };

  // Create styles for the card
  const cardStyle: React.CSSProperties = {
    position: 'relative',
    borderBottomWidth: '8px',
    borderBottomStyle: 'solid',
    borderBottomColor: borderColor,
    borderRadius: '8px',
    borderBottomLeftRadius: '6px',
    borderBottomRightRadius: '6px',
    transition: 'all 0.2s ease-in-out'
  };

  // Create hover styles for the card
  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    e.currentTarget.style.transform = 'translateY(-3px)';
    e.currentTarget.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
    e.currentTarget.style.borderBottomWidth = '10px';
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    e.currentTarget.style.transform = 'translateY(0)';
    e.currentTarget.style.boxShadow = '';
    e.currentTarget.style.borderBottomWidth = '8px';
  };

  return (
    <div
      className="module-card"
      style={cardStyle}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex flex-col h-full">
        {/* Header section with icon, title and badge */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              {getModuleIcon()}
            </div>
            <h3 className="font-semibold text-lg">{name}</h3>
          </div>
          <Badge
            variant={status === "active" ? "default" : "outline"}
            className={status === "active" ? "bg-green-500" : "text-muted-foreground"}
          >
            {status === "active" ? "Active" : "Inactive"}
          </Badge>
        </div>

        {/* Description with fixed height */}
        <div className="min-h-[60px] mb-8">
          <p className="text-muted-foreground text-sm line-clamp-3">{description}</p>
        </div>

        {/* Configure button positioned absolutely at bottom-right */}
        <div className="absolute bottom-6 right-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onConfigureClick(id, name)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>
    </div>
  );
};

const Modules = () => {
  const [modules, setModules] = useState<Omit<ModuleCardProps, 'onConfigureClick'>[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // State for the configure dialog
  const [configureDialogOpen, setConfigureDialogOpen] = useState<boolean>(false);
  const [selectedModuleId, setSelectedModuleId] = useState<string>('');
  const [selectedModuleName, setSelectedModuleName] = useState<string>('');
  const [dropdowns, setDropdowns] = useState<Dropdown[]>([]);
  const [loadingDropdowns, setLoadingDropdowns] = useState<boolean>(false);

  // State for the dropdown management dialog
  const [dropdownManagementOpen, setDropdownManagementOpen] = useState<boolean>(false);
  const [selectedDropdown, setSelectedDropdown] = useState<Dropdown | null>(null);

  // Handle configure button click
  const handleConfigureClick = async (moduleId: string, moduleName: string) => {
    setSelectedModuleId(moduleId);
    setSelectedModuleName(moduleName);
    setConfigureDialogOpen(true);
    setLoadingDropdowns(true);

    try {
      const dropdownsData = await fetchModuleDropdowns(moduleId);
      setDropdowns(dropdownsData);

      if (dropdownsData.length === 0) {
        toast({
          title: "No Dropdowns Found",
          description: `No dropdowns are available for ${moduleName}.`,
          variant: "default"
        });
      }
    } catch (error) {
      console.error(`Failed to fetch dropdowns for module ${moduleId}:`, error);
      toast({
        title: "Error",
        description: "Failed to fetch dropdowns. Please try again.",
        variant: "destructive"
      });
      setDropdowns([]);
    } finally {
      setLoadingDropdowns(false);
    }
  };

  // Fetch modules from API
  useEffect(() => {
    const fetchModulesData = async () => {
      setLoading(true);
      try {
        const modulesData = await getModules();

        // Map API modules to ModuleCardProps
        const mappedModules = modulesData.map(module => ({
          id: module.id,
          name: module.name,
          description: module.description,
          status: module.status, // Already converted to 'active' or 'inactive' in the service
          type: module.type as "risk" | "observation" | "tasks" | "permit" | "incident" | "knowledge" | "inspection",
          color: module.color // Pass the color from the API
        }));

        setModules(mappedModules);
        setError(null);
      } catch (err) {
        console.error('Error fetching modules:', err);
        setError('Failed to load modules. Please try again later.');

        // Fallback to hardcoded modules if API fails
        setModules([
          {
            id: "risk-module-1",
            name: "Integrated Risk Assessment",
            description: "Comprehensive risk assessment tool for identifying and evaluating potential hazards.",
            status: "active",
            type: "risk",
            color: "#E53935" // Default color for risk
          },
          {
            id: "observation-module-1",
            name: "Observation Reporting",
            description: "Tool for reporting and tracking safety observations across the organization.",
            status: "active",
            type: "observation",
            color: "#1E88E5" // Default color for observation
          },
          {
            id: "tasks-module-1",
            name: "Operational Tasks",
            description: "Manage and track operational tasks, assignments and follow-ups.",
            status: "active",
            type: "tasks",
            color: "#43A047" // Default color for tasks
          },
          {
            id: "permit-module-1",
            name: "ePermit to Work",
            description: "Electronic permit system for controlling and authorizing work activities.",
            status: "active",
            type: "permit",
            color: "#8E24AA" // Default color for permit
          },
          {
            id: "incident-module-1",
            name: "Incident Investigation",
            description: "Tool for recording, investigating and analyzing workplace incidents.",
            status: "active",
            type: "incident",
            color: "#FB8C00" // Default color for incident
          },
          {
            id: "knowledge-module-1",
            name: "Knowledge",
            description: "Knowledge management system for sharing best practices and lessons learned.",
            status: "inactive",
            type: "knowledge",
            color: "#00ACC1" // Default color for knowledge
          },
          {
            id: "inspection-module-1",
            name: "Inspection",
            description: "Create, schedule and perform inspections with customizable checklists.",
            status: "active",
            type: "inspection",
            color: "#6D4C41" // Default color for inspection
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchModulesData();
  }, []);

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title="Subscribed Modules"
          subtitle="Manage your organization's subscribed modules"
        />

        {loading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
            <p className="text-lg text-muted-foreground">Loading modules...</p>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-md p-6 text-center">
            <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <h3 className="text-lg font-semibold text-red-700 mb-2">Error Loading Modules</h3>
            <p className="text-red-600">{error}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </div>
        ) : modules.length === 0 ? (
          <div className="bg-amber-50 border border-amber-200 rounded-md p-6 text-center">
            <AlertOctagon className="h-8 w-8 text-amber-500 mx-auto mb-2" />
            <h3 className="text-lg font-semibold text-amber-700 mb-2">No Modules Found</h3>
            <p className="text-amber-600">No modules are currently available for your organization.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {modules.map((module) => (
              <ModuleCard
                key={module.name}
                id={module.id}
                name={module.name}
                description={module.description}
                status={module.status}
                type={module.type}
                color={module.color}
                onConfigureClick={handleConfigureClick}
              />
            ))}
          </div>
        )}
      </div>

      {/* Configure Dialog */}
      <Dialog open={configureDialogOpen} onOpenChange={setConfigureDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Configure {selectedModuleName}
            </DialogTitle>
          </DialogHeader>

          <div className="py-4">
            {loadingDropdowns ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                <p className="text-muted-foreground">Loading dropdowns...</p>
              </div>
            ) : dropdowns.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No dropdowns available for this module.</p>
              </div>
            ) : (
              <div className="space-y-4">
                <h3 className="font-medium text-lg mb-2">Available Dropdowns</h3>
                <ul className="space-y-2">
                  {dropdowns.map((dropdown) => (
                    <li
                      key={dropdown.id}
                      className="p-3 border rounded-md hover:bg-gray-50 cursor-pointer"
                      onClick={() => {
                        setSelectedDropdown(dropdown);
                        setDropdownManagementOpen(true);
                        setConfigureDialogOpen(false);
                      }}
                    >
                      <div className="font-medium">{dropdown.name}</div>
                      {dropdown.description && (
                        <div className="text-sm text-muted-foreground mt-1">{dropdown.description}</div>
                      )}
                      <div className="text-xs text-blue-500 mt-2">Click to manage dropdown items</div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setConfigureDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dropdown Management Dialog */}
      <Dialog
        open={dropdownManagementOpen}
        onOpenChange={(open) => {
          setDropdownManagementOpen(open);
          if (!open) {
            // When closing, reset the selected dropdown
            setSelectedDropdown(null);
          }
        }}
      >
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Manage {selectedDropdown?.name} Dropdown
            </DialogTitle>
          </DialogHeader>

          <div className="py-4">
            {selectedDropdown ? (
              <DynamicTab item={selectedDropdown} />
            ) : (
              <div className="flex flex-col items-center justify-center py-8">
                <p className="text-muted-foreground">No dropdown selected.</p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setDropdownManagementOpen(false);
                // Re-open the configure dialog when closing the dropdown management dialog
                setConfigureDialogOpen(true);
              }}
            >
              Back to Dropdowns
            </Button>
            <Button
              variant="default"
              onClick={() => setDropdownManagementOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
};

export default Modules;
