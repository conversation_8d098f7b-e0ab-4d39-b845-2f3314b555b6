<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cache</title>
</head>
<body>
    <h1>Clearing Cache...</h1>
    <script>
        // Clear localStorage
        localStorage.clear();
        
        // Clear sessionStorage
        sessionStorage.clear();
        
        // Clear any application-specific caches
        if (window.caches) {
            caches.keys().then(function(names) {
                for (let name of names) {
                    caches.delete(name);
                }
            });
        }
        
        // Display success message
        document.body.innerHTML += '<p>Cache cleared successfully!</p>';
        document.body.innerHTML += '<p>You can now close this window and return to the application.</p>';
    </script>
</body>
</html>
