import { api } from './apiService';
import { toast } from '@/components/ui/use-toast';

// API endpoints
const USERS_ENDPOINT = '/users/external';

// Interface definitions
export interface User {
  id?: string;
  firstName: string;
  email: string;
  company: string;
  type: 'Internal' | 'External';
  status?: 'active' | 'inactive';
  blocked?: boolean;
  dateAdded?: string;
  lastActive?: string;
  created?: string; // New field from API
}

/**
 * Fetch all users
 * @returns Promise with array of users
 */
export const fetchUsers = async (): Promise<User[]> => {
  try {
    console.log('Fetching users from API');
    const data = await api.get<Record<string, unknown>[]>(USERS_ENDPOINT);

    // Map the API response to our User interface
    // Convert boolean status to 'active' or 'inactive' string
    // Map created field to dateAdded if dateAdded is not present
    const mappedUsers: User[] = data.map(user => {
      const typedUser = user as Record<string, unknown>;
      return {
        id: typedUser.id as string,
        firstName: typedUser.firstName as string,
        email: typedUser.email as string,
        company: typedUser.company as string,
        type: typedUser.type as 'Internal' | 'External',
        status: typedUser.status === true ? 'active' : 'inactive',
        blocked: typedUser.blocked as boolean,
        dateAdded: (typedUser.dateAdded || typedUser.created) as string | undefined,
        lastActive: typedUser.lastActive as string | undefined,
        created: typedUser.created as string | undefined
      };
    });

    console.log('Users fetched successfully:', mappedUsers);
    return mappedUsers;
  } catch (error) {
    console.error('Failed to fetch users:', error);
    toast({
      title: 'Error',
      description: 'Failed to fetch users. Please try again.',
      variant: 'destructive',
    });
    return [];
  }
};

/**
 * Create a new user
 * @param userData The user data to create
 * @returns Promise with the created user
 */
export const createUser = async (userData: User): Promise<User | null> => {
  try {
    console.log('Creating new user:', userData);
    const response = await api.post<User>(USERS_ENDPOINT, userData);

    console.log('User created successfully:', response);
    toast({
      title: 'Success',
      description: 'User created successfully.',
    });

    return response;
  } catch (error) {
    console.error('Failed to create user:', error);
    toast({
      title: 'Error',
      description: 'Failed to create user. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Create a new user with a specific URL and format
 * @param userData The user data to create
 * @returns Promise with the created user
 */
export const createUserWithSpecificFormat = async (userData: User): Promise<User | null> => {
  try {
    console.log('Creating new user with specific format:', userData);

    // Create the user data in the specified format
    const formattedUserData = {
      firstName: userData.firstName,
      email: userData.email,
      company: userData.company,
      type: userData.type
    };

    // Use the specific URL for the POST request
    const response = await api.post<User>(USERS_ENDPOINT, formattedUserData);

    console.log('User created successfully:', response);
    toast({
      title: 'Success',
      description: 'User created successfully.',
    });

    return response;
  } catch (error) {
    console.error('Failed to create user:', error);
    toast({
      title: 'Error',
      description: 'Failed to create user. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Update a user
 * @param id The ID of the user to update
 * @param userData The data to update
 * @returns Promise with success flag
 */
export const updateUser = async (id: string, userData: Partial<User>): Promise<boolean> => {
  try {
    console.log(`Updating user ${id} with:`, userData);

    // Convert status from string to boolean if it exists
    const apiUserData = { ...userData };
    if (apiUserData.status !== undefined) {
      // @ts-expect-error - We're intentionally converting string to boolean
      apiUserData.status = apiUserData.status === 'active';
    }

    await api.patch<Record<string, unknown>>(`${USERS_ENDPOINT}/${id}`, apiUserData);

    console.log('User updated successfully');
    toast({
      title: 'Success',
      description: 'User updated successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to update user:', error);
    toast({
      title: 'Error',
      description: 'Failed to update user. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Update a user with a specific URL
 * @param id The ID of the user to update
 * @param userData The data to update
 * @returns Promise with success flag
 */
export const updateUserWithSpecificUrl = async (id: string, userData: Partial<User>): Promise<boolean> => {
  try {
    console.log(`Updating user ${id} with:`, userData);

    // Convert status from string to boolean if it exists
    const apiUserData = { ...userData };
    if (apiUserData.status !== undefined) {
      // @ts-expect-error - We're intentionally converting string to boolean
      apiUserData.status = apiUserData.status === 'active';
    }

    // Use the specific URL for the PATCH request
    await api.patch<Record<string, unknown>>(`/users/${id}`, apiUserData);

    console.log('User updated successfully');
    toast({
      title: 'Success',
      description: 'User updated successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to update user:', error);
    toast({
      title: 'Error',
      description: 'Failed to update user. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Delete a user
 * @param id The ID of the user to delete
 * @returns Promise with success flag
 */
export const deleteUser = async (id: string): Promise<boolean> => {
  try {
    console.log(`Deleting user ${id}`);
    await api.delete(`${USERS_ENDPOINT}/${id}`);

    console.log('User deleted successfully');
    toast({
      title: 'Success',
      description: 'User deleted successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to delete user:', error);
    toast({
      title: 'Error',
      description: 'Failed to delete user. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Reset MFA for a user
 * @param email The email of the user to reset MFA for
 * @returns Promise with success flag
 */
export const resetUserMFA = async (email: string): Promise<boolean> => {
  try {
    console.log(`Resetting MFA for user with email: ${email}`);

    // Use the specific URL for the POST request
    await api.post<Record<string, unknown>>(`${USERS_ENDPOINT}/external/reset-mfa`, { email });

    console.log('MFA reset successfully');
    toast({
      title: 'Success',
      description: 'Authentication reset successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to reset MFA:', error);
    toast({
      title: 'Error',
      description: 'Failed to reset authentication. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};
