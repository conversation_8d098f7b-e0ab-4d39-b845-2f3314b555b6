import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/contexts/AuthContext";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { setCurrentPath, resetNavigation } from "@/store/slices/navigationSlice";
import NavLink from "@/components/navigation/NavLink";
import {
  Home,
  Layout,
  Settings,
  LogOut,
  Users,
  Activity,
  Building2,
  Briefcase,
  Languages,
  UserCog,
  SlidersHorizontal
} from "lucide-react";

interface SidebarProps {
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
}

type NavItem = {
  title: string;
  href: string;
  icon: React.ElementType;
  isActive?: boolean;
  subItems?: NavItem[];
  onClick?: (e: React.MouseEvent) => void;
};

const Sidebar = ({ isCollapsed, setIsCollapsed }: SidebarProps) => {
  const location = useLocation();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { currentPath } = useAppSelector((state) => state.navigation);
  const [expandedGroup, setExpandedGroup] = useState<string | null>("general-config");

  // Update Redux store when location changes
  useEffect(() => {
    if (location.pathname !== currentPath) {
      dispatch(setCurrentPath(location.pathname));
    }
  }, [location.pathname, dispatch, currentPath]);

  const toggleGroup = (groupName: string) => {
    if (isCollapsed) {
      setIsCollapsed(false);
      setExpandedGroup(groupName);
      return;
    }

    if (expandedGroup === groupName) {
      setExpandedGroup(null);
    } else {
      setExpandedGroup(groupName);
    }
  };

  const mainNavItems: NavItem[] = [
    {
      title: t("navigation.home"),
      href: "/",
      icon: Home,
      isActive: currentPath === "/",
    },
    {
      title: t("navigation.modules"),
      href: "/modules",
      icon: Layout,
      isActive: currentPath === "/modules",
    },
    {
      title: t("navigation.generalConfig"),
      href: "#",
      icon: SlidersHorizontal,
      isActive: ["/entity-hierarchy", "/departments", "/work-activities", "/languages"].includes(currentPath),
      subItems: [
        {
          title: t("navigation.entityHierarchy"),
          href: "/entity-hierarchy",
          icon: Building2,
          isActive: currentPath === "/entity-hierarchy",
        },
        {
          title: t("navigation.workAreas"),
          href: "/departments",
          icon: Briefcase,
          isActive: currentPath === "/departments",
        },
        {
          title: t("navigation.workActivities"),
          href: "/work-activities",
          icon: Briefcase,
          isActive: currentPath === "/work-activities",
        },
        {
          title: t("navigation.languages"),
          href: "/languages",
          icon: Languages,
          isActive: currentPath === "/languages",
        },
      ],
    },
    {
      title: t("navigation.userManagement"),
      href: "/users",
      icon: Users,
      isActive: currentPath === "/users",
    },
    {
      title: t("navigation.roleAssignment"),
      href: "/role-assignment",
      icon: UserCog,
      isActive: currentPath === "/role-assignment",
    },
    {
      title: t("navigation.activityLog"),
      href: "/activity",
      icon: Activity,
      isActive: currentPath === "/activity",
    },
  ];

  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async (e: React.MouseEvent) => {
    e.preventDefault();
    // Reset navigation state before logout
    dispatch(resetNavigation());
    await logout();
    // Navigate to login page after logout
    navigate('/login');
  };

  const footerNavItems: NavItem[] = [
    {
      title: t("navigation.settings"),
      href: "/settings",
      icon: Settings,
      isActive: currentPath === "/settings",
    },
    {
      title: t("auth.logout"),
      href: "#",
      icon: LogOut,
      onClick: handleLogout,
    },
  ];

  return (
    <aside
      className={cn(
        "bg-sidebar transition-all duration-200 ease-in-out cursor-pointer h-[calc(100vh-6rem)] sticky top-24 z-30",
        isCollapsed ? "w-16" : "w-64"
      )}
      onClick={() => setIsCollapsed(!isCollapsed)}
    >
      <div className="h-full flex flex-col justify-between">
        <div>
          <nav className="mt-6 px-2 overflow-y-auto">
            <ul className="space-y-2">
              {mainNavItems.map((item) => (
                <li key={item.title}>
                  {item.subItems ? (
                    <>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleGroup(item.title.toLowerCase().replace(/\s/g, "-"));
                        }}
                        className={cn(
                          "w-full flex items-center px-3 py-2 rounded-md transition-colors",
                          item.isActive
                            ? "bg-sidebar-accent text-sidebar-accent-foreground"
                            : "text-sidebar-foreground hover:bg-sidebar-accent/50",
                          isCollapsed && "justify-center"
                        )}
                      >
                        <item.icon size={20} />
                        {!isCollapsed && (
                          <>
                            <span className="ml-3">{item.title}</span>
                            <div className="ml-auto w-4 h-4 flex items-center justify-center">
                              <div className={cn(
                                "w-2 h-2 border-r-2 border-b-2 border-current transform transition-transform duration-200",
                                expandedGroup === item.title.toLowerCase().replace(/\s/g, "-") ? "rotate-45" : "-rotate-45"
                              )} />
                            </div>
                          </>
                        )}
                      </button>
                      {!isCollapsed && expandedGroup === item.title.toLowerCase().replace(/\s/g, "-") && (
                        <ul className="mt-1 ml-6 space-y-1">
                          {item.subItems.map((subItem) => (
                            <li key={subItem.title}>
                              <NavLink
                                to={subItem.href}
                                className={cn(
                                  "flex items-center px-3 py-2 rounded-md transition-colors",
                                  subItem.isActive
                                    ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                    : "text-sidebar-foreground hover:bg-sidebar-accent/50"
                                )}
                                onClick={(e) => e.stopPropagation()}
                              >
                                <subItem.icon size={16} />
                                <span className="ml-3 text-sm">{subItem.title}</span>
                              </NavLink>
                            </li>
                          ))}
                        </ul>
                      )}
                    </>
                  ) : (
                    <NavLink
                      to={item.href}
                      className={cn(
                        "flex items-center px-3 py-2 rounded-md transition-colors",
                        item.isActive
                          ? "bg-sidebar-accent text-sidebar-accent-foreground"
                          : "text-sidebar-foreground hover:bg-sidebar-accent/50",
                        isCollapsed && "justify-center"
                      )}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <item.icon size={20} />
                      {!isCollapsed && <span className="ml-3">{item.title}</span>}
                    </NavLink>
                  )}
                </li>
              ))}
            </ul>
          </nav>
        </div>

        <nav className="mt-6 px-2 mb-6">
          <ul className="space-y-2">
            {footerNavItems.map((item) => (
              <li key={item.title}>
                <NavLink
                  to={item.href}
                  className={cn(
                    "flex items-center px-3 py-2 rounded-md transition-colors",
                    item.isActive
                      ? "bg-sidebar-accent text-sidebar-accent-foreground"
                      : "text-sidebar-foreground hover:bg-sidebar-accent/50",
                    isCollapsed && "justify-center"
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (item.onClick) {
                      item.onClick(e);
                    }
                  }}
                >
                  <item.icon size={20} />
                  {!isCollapsed && <span className="ml-3">{item.title}</span>}
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;
