import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Define available languages
export const AVAILABLE_LANGUAGES = [
  { id: "1", name: "English", code: "en-US", enabled: true, isDefault: true },
  { id: "2", name: "Spanish", code: "es-ES", enabled: true, isDefault: false },
  { id: "3", name: "French", code: "fr-FR", enabled: true, isDefault: false },
  { id: "4", name: "German", code: "de-DE", enabled: false, isDefault: false },
  { id: "5", name: "Chinese (Simplified)", code: "zh-CN", enabled: true, isDefault: false },
  { id: "6", name: "Japanese", code: "ja-JP", enabled: false, isDefault: false },
  { id: "7", name: "Portuguese", code: "pt-BR", enabled: false, isDefault: false },
  { id: "8", name: "Arabic", code: "ar-SA", enabled: true, isDefault: false },
  { id: "9", name: "Russian", code: "ru-RU", enabled: false, isDefault: false },
  { id: "10", name: "Tamil", code: "ta-IN", enabled: true, isDefault: false },
  { id: "11", name: "Hindi", code: "hi-IN", enabled: true, isDefault: false },
  { id: "12", name: "Sinhalese", code: "si-LK", enabled: true, isDefault: false }
];

// Get language code from language object
export const getLanguageCode = (langCode: string): string => {
  // If the language code contains a region (e.g., en-US), return just the language part
  return langCode.split('-')[0];
};

// Check if a language is RTL (Right-to-Left)
export const isRTL = (langCode: string): boolean => {
  const code = getLanguageCode(langCode);
  return ['ar', 'he', 'fa', 'ur'].includes(code);
};

// Initialize i18next
i18n
  // Load translations using http backend
  .use(Backend)
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    // Default language
    fallbackLng: 'en',
    // Debug mode
    debug: process.env.NODE_ENV === 'development',
    // Namespace
    defaultNS: 'common',
    ns: ['common'],
    // Language mapping to handle locale-specific codes
    load: 'languageOnly', // Only load the language part of the code (e.g., 'en' from 'en-US')
    // Backend configuration
    backend: {
      // Path to load translations from
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
    // Detection options
    detection: {
      // Order of language detection
      order: ['localStorage', 'navigator'],
      // Cache language in localStorage
      caches: ['localStorage'],
      // localStorage key
      lookupLocalStorage: 'i18nextLng',
    },
    // Interpolation options
    interpolation: {
      // React already escapes values
      escapeValue: false,
    },
    // React options
    react: {
      // Wait for translations to be loaded
      useSuspense: true,
    },
  });

export default i18n;
