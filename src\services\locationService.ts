import { api } from './apiService';
import storageService from './storageService';

// Base types for locations
export interface BaseLocation {
  id: string;
  name: string;
  status: boolean;
  created: string;
  updated: string;
}

// Location One (top level)
export interface LocationOne extends BaseLocation {
  // Additional fields specific to LocationOne if any
}

// Location Two (second level)
export interface LocationTwo extends BaseLocation {
  locationOneId: string; // Reference to parent LocationOne
}

// Location Three (third level)
export interface LocationThree extends BaseLocation {
  locationTwoId: string; // Reference to parent LocationTwo
}

// Location Four (fourth level)
export interface LocationFour extends BaseLocation {
  locationThreeId: string; // Reference to parent LocationThree
}

// Location Five (fifth level)
export interface LocationFive extends BaseLocation {
  locationFourId: string; // Reference to parent LocationFour
}

// Union type for all location types
export type Location =
  | LocationOne
  | LocationTwo
  | LocationThree
  | LocationFour
  | LocationFive;

// API endpoints for each location level
const API_ENDPOINTS = {
  locationOne: '/location-ones',
  locationTwo: '/location-twos',
  locationThree: '/location-threes',
  locationFour: '/location-fours',
  locationFive: '/location-fives'
};

// Storage keys for caching
const STORAGE_KEYS = {
  locationOne: 'location_ones',
  locationTwo: 'location_twos',
  locationThree: 'location_threes',
  locationFour: 'location_fours',
  locationFive: 'location_fives'
};

// Cache expiration time (1 hour)
const CACHE_EXPIRATION = 3600000;

/**
 * Fetch all LocationOne entities
 * @returns Promise with array of LocationOne entities
 */
export const fetchLocationOnes = async (): Promise<LocationOne[]> => {
  try {
    console.log('Fetching LocationOne entities');
    const data = await api.get<LocationOne[]>(API_ENDPOINTS.locationOne);

    // Sort the data by name for better usability
    const sortedData = [...data].sort((a, b) => a.name.localeCompare(b.name));

    // Cache the sorted data
    storageService.setItem(STORAGE_KEYS.locationOne, sortedData, CACHE_EXPIRATION);

    return sortedData;
  } catch (error) {
    console.error('Failed to fetch LocationOne entities:', error);

    // Try to get from cache if API fails
    const cachedData = storageService.getItem<LocationOne[]>(STORAGE_KEYS.locationOne);
    return cachedData || [];
  }
};

/**
 * Fetch LocationTwo entities for a specific LocationOne parent
 * @param locationOneId The ID of the parent LocationOne
 * @returns Promise with array of LocationTwo entities
 */
export const fetchLocationTwos = async (locationOneId: string): Promise<LocationTwo[]> => {
  try {
    console.log(`Fetching LocationTwo entities for LocationOne ${locationOneId}`);
    // Use the direct parent/child relationship URL pattern
    const data = await api.get<LocationTwo[]>(`${API_ENDPOINTS.locationOne}/${locationOneId}/location-twos`);

    // Sort the data by name for better usability
    const sortedData = [...data].sort((a, b) => a.name.localeCompare(b.name));

    // Cache the sorted data with the parent ID in the key
    storageService.setItem(`${STORAGE_KEYS.locationTwo}_${locationOneId}`, sortedData, CACHE_EXPIRATION);

    return sortedData;
  } catch (error) {
    console.error(`Failed to fetch LocationTwo entities for LocationOne ${locationOneId}:`, error);

    // Try to get from cache if API fails
    const cachedData = storageService.getItem<LocationTwo[]>(`${STORAGE_KEYS.locationTwo}_${locationOneId}`);
    return cachedData || [];
  }
};

/**
 * Fetch LocationThree entities for a specific LocationTwo parent
 * @param locationTwoId The ID of the parent LocationTwo
 * @returns Promise with array of LocationThree entities
 */
export const fetchLocationThrees = async (locationTwoId: string): Promise<LocationThree[]> => {
  try {
    console.log(`Fetching LocationThree entities for LocationTwo ${locationTwoId}`);
    // Use the direct parent/child relationship URL pattern
    const data = await api.get<LocationThree[]>(`${API_ENDPOINTS.locationTwo}/${locationTwoId}/location-threes`);

    // Sort the data by name for better usability
    const sortedData = [...data].sort((a, b) => a.name.localeCompare(b.name));

    // Cache the sorted data with the parent ID in the key
    storageService.setItem(`${STORAGE_KEYS.locationThree}_${locationTwoId}`, sortedData, CACHE_EXPIRATION);

    return sortedData;
  } catch (error) {
    console.error(`Failed to fetch LocationThree entities for LocationTwo ${locationTwoId}:`, error);

    // Try to get from cache if API fails
    const cachedData = storageService.getItem<LocationThree[]>(`${STORAGE_KEYS.locationThree}_${locationTwoId}`);
    return cachedData || [];
  }
};

/**
 * Fetch LocationFour entities for a specific LocationThree parent
 * @param locationThreeId The ID of the parent LocationThree
 * @returns Promise with array of LocationFour entities
 */
export const fetchLocationFours = async (locationThreeId: string): Promise<LocationFour[]> => {
  try {
    console.log(`Fetching LocationFour entities for LocationThree ${locationThreeId}`);
    // Use the direct parent/child relationship URL pattern
    const data = await api.get<LocationFour[]>(`${API_ENDPOINTS.locationThree}/${locationThreeId}/location-fours`);

    // Sort the data by name for better usability
    const sortedData = [...data].sort((a, b) => a.name.localeCompare(b.name));

    // Cache the sorted data with the parent ID in the key
    storageService.setItem(`${STORAGE_KEYS.locationFour}_${locationThreeId}`, sortedData, CACHE_EXPIRATION);

    return sortedData;
  } catch (error) {
    console.error(`Failed to fetch LocationFour entities for LocationThree ${locationThreeId}:`, error);

    // Try to get from cache if API fails
    const cachedData = storageService.getItem<LocationFour[]>(`${STORAGE_KEYS.locationFour}_${locationThreeId}`);
    return cachedData || [];
  }
};

/**
 * Fetch LocationFive entities for a specific LocationFour parent
 * @param locationFourId The ID of the parent LocationFour
 * @returns Promise with array of LocationFive entities
 */
export const fetchLocationFives = async (locationFourId: string): Promise<LocationFive[]> => {
  try {
    console.log(`Fetching LocationFive entities for LocationFour ${locationFourId}`);
    // Use the direct parent/child relationship URL pattern
    const data = await api.get<LocationFive[]>(`${API_ENDPOINTS.locationFour}/${locationFourId}/location-fives`);

    // Sort the data by name for better usability
    const sortedData = [...data].sort((a, b) => a.name.localeCompare(b.name));

    // Cache the sorted data with the parent ID in the key
    storageService.setItem(`${STORAGE_KEYS.locationFive}_${locationFourId}`, sortedData, CACHE_EXPIRATION);

    return sortedData;
  } catch (error) {
    console.error(`Failed to fetch LocationFive entities for LocationFour ${locationFourId}:`, error);

    // Try to get from cache if API fails
    const cachedData = storageService.getItem<LocationFive[]>(`${STORAGE_KEYS.locationFive}_${locationFourId}`);
    return cachedData || [];
  }
};

/**
 * Create a new LocationOne entity
 * @param name The name of the LocationOne to create
 * @returns Promise with the created LocationOne entity
 */
export const createLocationOne = async (name: string): Promise<LocationOne | null> => {
  try {
    console.log('Creating new LocationOne entity:', name);
    const response = await api.post<LocationOne>(API_ENDPOINTS.locationOne, { name });

    // Invalidate cache
    storageService.removeItem(STORAGE_KEYS.locationOne);

    return response;
  } catch (error) {
    console.error('Failed to create LocationOne entity:', error);
    return null;
  }
};

/**
 * Create a new LocationTwo entity
 * @param name The name of the LocationTwo to create
 * @param locationOneId The ID of the parent LocationOne
 * @returns Promise with the created LocationTwo entity
 */
export const createLocationTwo = async (name: string, locationOneId: string): Promise<LocationTwo | null> => {
  try {
    console.log('Creating new LocationTwo entity:', name, 'under parent:', locationOneId);
    const response = await api.post<LocationTwo>(API_ENDPOINTS.locationTwo, {
      name,
      locationOneId
    });

    // Invalidate cache
    storageService.removeItem(`${STORAGE_KEYS.locationTwo}_${locationOneId}`);

    return response;
  } catch (error) {
    console.error('Failed to create LocationTwo entity:', error);
    return null;
  }
};

/**
 * Create a new LocationThree entity
 * @param name The name of the LocationThree to create
 * @param locationTwoId The ID of the parent LocationTwo
 * @returns Promise with the created LocationThree entity
 */
export const createLocationThree = async (name: string, locationTwoId: string): Promise<LocationThree | null> => {
  try {
    console.log('Creating new LocationThree entity:', name, 'under parent:', locationTwoId);
    const response = await api.post<LocationThree>(API_ENDPOINTS.locationThree, {
      name,
      locationTwoId
    });

    // Invalidate cache
    storageService.removeItem(`${STORAGE_KEYS.locationThree}_${locationTwoId}`);

    return response;
  } catch (error) {
    console.error('Failed to create LocationThree entity:', error);
    return null;
  }
};

/**
 * Create a new LocationFour entity
 * @param name The name of the LocationFour to create
 * @param locationThreeId The ID of the parent LocationThree
 * @returns Promise with the created LocationFour entity
 */
export const createLocationFour = async (name: string, locationThreeId: string): Promise<LocationFour | null> => {
  try {
    console.log('Creating new LocationFour entity:', name, 'under parent:', locationThreeId);
    const response = await api.post<LocationFour>(API_ENDPOINTS.locationFour, {
      name,
      locationThreeId
    });

    // Invalidate cache
    storageService.removeItem(`${STORAGE_KEYS.locationFour}_${locationThreeId}`);

    return response;
  } catch (error) {
    console.error('Failed to create LocationFour entity:', error);
    return null;
  }
};

/**
 * Create a new LocationFive entity
 * @param name The name of the LocationFive to create
 * @param locationFourId The ID of the parent LocationFour
 * @returns Promise with the created LocationFive entity
 */
export const createLocationFive = async (name: string, locationFourId: string): Promise<LocationFive | null> => {
  try {
    console.log('Creating new LocationFive entity:', name, 'under parent:', locationFourId);
    const response = await api.post<LocationFive>(API_ENDPOINTS.locationFive, {
      name,
      locationFourId
    });

    // Invalidate cache
    storageService.removeItem(`${STORAGE_KEYS.locationFive}_${locationFourId}`);

    return response;
  } catch (error) {
    console.error('Failed to create LocationFive entity:', error);
    return null;
  }
};

/**
 * Update a location entity
 * @param level The level of the location (locationOne, locationTwo, etc.)
 * @param id The ID of the location to update
 * @param data The data to update
 * @returns Promise with the updated location entity or success flag
 */
export const updateLocation = async (
  level: keyof typeof API_ENDPOINTS,
  id: string,
  data: Partial<Location>
): Promise<Location | { success: boolean } | null> => {
  try {
    console.log(`Updating ${level} entity ${id}:`, data);

    // Make the API call
    const response = await api.patch<any>(`${API_ENDPOINTS[level]}/${id}`, data);

    // Determine parent ID field based on level
    let parentIdField: string | null = null;
    switch (level) {
      case 'locationTwo':
        parentIdField = 'locationOneId';
        break;
      case 'locationThree':
        parentIdField = 'locationTwoId';
        break;
      case 'locationFour':
        parentIdField = 'locationThreeId';
        break;
      case 'locationFive':
        parentIdField = 'locationFourId';
        break;
      default:
        parentIdField = null;
    }

    // Invalidate appropriate cache
    if (level === 'locationOne') {
      storageService.removeItem(STORAGE_KEYS[level]);
    } else if (parentIdField && data[parentIdField as keyof typeof data]) {
      const parentId = data[parentIdField as keyof typeof data] as string;
      storageService.removeItem(`${STORAGE_KEYS[level]}_${parentId}`);
    }

    // Handle response
    if (response && typeof response === 'object') {
      return response;
    } else {
      return { success: true };
    }
  } catch (error) {
    console.error(`Failed to update ${level} entity ${id}:`, error);

    // Check if it's a 204 No Content response (success with no body)
    if (error && typeof error === 'object' && 'status' in error) {
      const status = (error as any).status;
      if (status === 204 || status === 200) {
        return { success: true };
      }
    }

    return null;
  }
};

/**
 * Delete a location entity
 * @param level The level of the location (locationOne, locationTwo, etc.)
 * @param id The ID of the location to delete
 * @param parentId The ID of the parent location (for cache invalidation)
 * @returns Promise with success flag
 */
export const deleteLocation = async (
  level: keyof typeof API_ENDPOINTS,
  id: string,
  parentId?: string
): Promise<boolean> => {
  try {
    console.log(`Deleting ${level} entity ${id}`);

    // Make the API call
    await api.delete(`${API_ENDPOINTS[level]}/${id}`);

    // Invalidate appropriate cache
    if (level === 'locationOne') {
      storageService.removeItem(STORAGE_KEYS[level]);
    } else if (parentId) {
      storageService.removeItem(`${STORAGE_KEYS[level]}_${parentId}`);
    }

    return true;
  } catch (error) {
    console.error(`Failed to delete ${level} entity ${id}:`, error);

    // Check if it's a 204 No Content response (success with no body)
    if (error && typeof error === 'object' && 'status' in error) {
      const status = (error as any).status;
      if (status === 204 || status === 200) {
        return true;
      }
    }

    return false;
  }
};

/**
 * Reorder location entities
 * @param level The level of the location (locationOne, locationTwo, etc.)
 * @param entityIds Array of entity IDs in the new order
 * @param parentId The ID of the parent location (for cache invalidation)
 * @returns Promise with success flag
 */
export const reorderLocations = async (
  level: keyof typeof API_ENDPOINTS,
  entityIds: string[],
  parentId?: string
): Promise<boolean> => {
  try {
    console.log(`Reordering ${level} entities:`, entityIds);

    // Make the API call to reorder entities
    // Note: This endpoint may need to be implemented on the backend
    await api.patch(`${API_ENDPOINTS[level]}/reorder`, {
      entityIds,
      parentId
    });

    // Invalidate appropriate cache
    if (level === 'locationOne') {
      storageService.removeItem(STORAGE_KEYS[level]);
    } else if (parentId) {
      storageService.removeItem(`${STORAGE_KEYS[level]}_${parentId}`);
    }

    return true;
  } catch (error) {
    console.error(`Failed to reorder ${level} entities:`, error);

    // For now, return true to allow local reordering even if backend doesn't support it
    // This can be changed to return false once the backend endpoint is implemented
    console.warn(`Reordering API not implemented for ${level}, continuing with local reordering only`);
    return true;
  }
};

// Export default object with all functions
export default {
  // Fetch functions
  fetchLocationOnes,
  fetchLocationTwos,
  fetchLocationThrees,
  fetchLocationFours,
  fetchLocationFives,

  // Create functions
  createLocationOne,
  createLocationTwo,
  createLocationThree,
  createLocationFour,
  createLocationFive,

  // Update, delete, and reorder functions
  updateLocation,
  deleteLocation,
  reorderLocations
};