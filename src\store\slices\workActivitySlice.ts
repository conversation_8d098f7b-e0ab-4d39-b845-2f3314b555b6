import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  WorkActivity,
  fetchWorkActivities as fetchWorkActivitiesAPI,
  createWorkActivity as createWorkActivityAPI,
  updateWorkActivity as updateWorkActivityAPI,
  deleteWorkActivity as deleteWorkActivityAPI
} from '@/services/workActivityService';

// Define the state interface
interface WorkActivityState {
  workActivities: WorkActivity[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: WorkActivityState = {
  workActivities: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchWorkActivities = createAsyncThunk(
  'workActivities/fetchWorkActivities',
  async (_, { rejectWithValue }) => {
    try {
      return await fetchWorkActivitiesAPI();
    } catch (error) {
      return rejectWithValue('Failed to fetch work activities');
    }
  }
);

export const createWorkActivity = createAsyncThunk(
  'workActivities/createWorkActivity',
  async (name: string, { rejectWithValue }) => {
    try {
      const activity = await createWorkActivityAPI(name);
      if (!activity) {
        return rejectWithValue('Failed to create work activity');
      }
      return activity;
    } catch (error) {
      return rejectWithValue('Failed to create work activity');
    }
  }
);

export const updateWorkActivity = createAsyncThunk(
  'workActivities/updateWorkActivity',
  async ({ activityId, name }: { activityId: string; name: string }, { rejectWithValue }) => {
    try {
      const success = await updateWorkActivityAPI(activityId, name);
      if (!success) {
        return rejectWithValue('Failed to update work activity');
      }
      return { id: activityId, name };
    } catch (error) {
      return rejectWithValue('Failed to update work activity');
    }
  }
);

export const deleteWorkActivity = createAsyncThunk(
  'workActivities/deleteWorkActivity',
  async (activityId: string, { rejectWithValue }) => {
    try {
      const success = await deleteWorkActivityAPI(activityId);
      if (!success) {
        return rejectWithValue('Failed to delete work activity');
      }
      return activityId;
    } catch (error) {
      return rejectWithValue('Failed to delete work activity');
    }
  }
);

// Create the slice
const workActivitySlice = createSlice({
  name: 'workActivities',
  initialState,
  reducers: {
    clearWorkActivityError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch work activities
      .addCase(fetchWorkActivities.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWorkActivities.fulfilled, (state, action) => {
        state.isLoading = false;
        state.workActivities = action.payload;
      })
      .addCase(fetchWorkActivities.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Create work activity
      .addCase(createWorkActivity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWorkActivity.fulfilled, (state, action) => {
        state.isLoading = false;
        state.workActivities.push(action.payload);
      })
      .addCase(createWorkActivity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Update work activity
      .addCase(updateWorkActivity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateWorkActivity.fulfilled, (state, action) => {
        state.isLoading = false;
        const { id, name } = action.payload;
        const activity = state.workActivities.find(act => act.id === id);
        if (activity) {
          activity.name = name;
        }
      })
      .addCase(updateWorkActivity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Delete work activity
      .addCase(deleteWorkActivity.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteWorkActivity.fulfilled, (state, action) => {
        state.isLoading = false;
        const activityId = action.payload;
        state.workActivities = state.workActivities.filter(act => act.id !== activityId);
      })
      .addCase(deleteWorkActivity.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearWorkActivityError } = workActivitySlice.actions;

export default workActivitySlice.reducer;
