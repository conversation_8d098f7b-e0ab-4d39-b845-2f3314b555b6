/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .direction-rtl {
  flex-direction: row-reverse;
}

[dir="rtl"] .ml-auto {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .mr-auto {
  margin-right: 0;
  margin-left: auto;
}

[dir="rtl"] .ml-1 {
  margin-left: 0;
  margin-right: 0.25rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-1 {
  margin-right: 0;
  margin-left: 0.25rem;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .pl-1 {
  padding-left: 0;
  padding-right: 0.25rem;
}

[dir="rtl"] .pl-2 {
  padding-left: 0;
  padding-right: 0.5rem;
}

[dir="rtl"] .pl-3 {
  padding-left: 0;
  padding-right: 0.75rem;
}

[dir="rtl"] .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

[dir="rtl"] .pr-1 {
  padding-right: 0;
  padding-left: 0.25rem;
}

[dir="rtl"] .pr-2 {
  padding-right: 0;
  padding-left: 0.5rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0;
  padding-left: 0.75rem;
}

[dir="rtl"] .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

[dir="rtl"] .pl-9 {
  padding-left: 0;
  padding-right: 2.25rem;
}

/* RTL for search icon */
[dir="rtl"] .absolute.left-3 {
  left: auto;
  right: 0.75rem;
}

/* RTL for dropdown menus */
[dir="rtl"] [data-side="right"] {
  margin-left: 0;
  margin-right: 0.5rem;
}

/* RTL for icons in buttons */
[dir="rtl"] .mr-2.h-4.w-4 {
  margin-right: 0;
  margin-left: 0.5rem;
}
