/**
 * AWS Cognito Configuration
 *
 * This file contains the configuration for AWS Cognito authentication.
 */

import { Amplify } from 'aws-amplify';
import { cognitoUserPoolsTokenProvider } from 'aws-amplify/auth/cognito';
import { LoginConfig, defaultConfig } from '@/services/loginConfigService';

// Cognito configuration
export const getCognitoConfig = (loginConfig: LoginConfig = defaultConfig) => {
  return {
    region: loginConfig.COGNITO_REGION || 'ap-southeast-1',
    userPoolId: loginConfig.COGNITO_ADMIN_USER_POOL_ID || '',
    userPoolWebClientId: loginConfig.COGNITO_ADMIN_APP_CLIENT_ID || '',
    oauth: {
      domain: loginConfig.COGNITO_ADMIN_DOMAIN || '',
      scope: ['email', 'profile', 'openid'],
      redirectSignIn: import.meta.env.VITE_AWS_REDIRECT_SIGN_IN || window.location.origin,
      redirectSignOut: import.meta.env.VITE_AWS_REDIRECT_SIGN_OUT || window.location.origin,
      responseType: 'code',
    },
  };
};

// Default configuration
export const cognitoConfig = getCognitoConfig();

/**
 * Initialize AWS Amplify with Cognito configuration
 */
export const initializeAmplify = (loginConfig?: LoginConfig) => {
  const config = loginConfig ? getCognitoConfig(loginConfig) : cognitoConfig;

  Amplify.configure({
    Auth: {
      Cognito: {
        userPoolId: config.userPoolId,
        userPoolClientId: config.userPoolWebClientId,
        loginWith: {
          oauth: {
            domain: config.oauth.domain,
            scopes: config.oauth.scope,
            redirectSignIn: [config.oauth.redirectSignIn],
            redirectSignOut: [config.oauth.redirectSignOut],
            responseType: 'code'
          }
        }
      }
    }
  });

  return config;
};

/**
 * Check if Cognito is properly configured
 */
export const isCognitoConfigured = (config = cognitoConfig): boolean => {
  return !!(
    config.userPoolId &&
    config.userPoolWebClientId
  );
};

export default {
  cognitoConfig,
  getCognitoConfig,
  initializeAmplify,
  isCognitoConfigured,
};
