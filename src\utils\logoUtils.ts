import axios from 'axios';
import { LoginConfig } from '@/services/loginConfigService';

/**
 * Fetch a presigned URL for a logo file
 * @param filename The filename of the logo
 * @returns The presigned URL or an empty string if the request fails
 */
export const fetchLogoPresignedUrl = async (filename: string): Promise<string> => {
  try {
    console.log('Fetching presigned URL for logo:', filename);

    // Use CORS proxy if provided
    const CORS_PROXY_URL = import.meta.env.VITE_CORS_PROXY_URL || '';

    // Construct the correct URL format for the presigned URL endpoint
    const apiUrl = CORS_PROXY_URL
      ? `${CORS_PROXY_URL}https://admin.client-api.acuizen.com/files/${encodeURIComponent(filename)}/presigned-url`
      : `https://admin.client-api.acuizen.com/files/${encodeURIComponent(filename)}/presigned-url`;

    console.log('Requesting presigned URL from:', apiUrl);

    // Get access token from localStorage
    const accessToken = localStorage.getItem('access_token');

    const response = await axios.get(apiUrl, {
      timeout: 5000, // 5 second timeout
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {})
      }
    });

    console.log('Presigned URL response:', response.data);

    // The response might directly be the URL or have a url property
    const presignedUrl = typeof response.data === 'string' ? response.data : response.data.url;
    console.log('Extracted presigned URL:', presignedUrl);

    return presignedUrl || '';
  } catch (error) {
    console.error('Failed to fetch presigned URL for logo:', error);
    if (axios.isAxiosError(error)) {
      console.error('Axios error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
    }
    return '';
  }
};

/**
 * Get a logo URL from a config, with caching
 * @param config The login configuration
 * @param cacheKey The key to use for caching in localStorage
 * @returns An object with the logo URL and a loading state
 */
export const getLogoUrl = async (
  config: LoginConfig | null,
  cacheKey: string = 'headerLogoUrl'
): Promise<{ logoUrl: string; isLoading: boolean }> => {
  // Check if we have cached logo URL
  const cachedLogoUrl = localStorage.getItem(cacheKey);
  const cachedLogoTimestamp = localStorage.getItem(`${cacheKey}Timestamp`);
  const logoExpiration = 3600000; // 1 hour in milliseconds

  // If we have a cached logo URL and it's not expired, use it
  if (cachedLogoUrl && cachedLogoTimestamp) {
    const timestamp = parseInt(cachedLogoTimestamp, 10);
    const now = Date.now();

    if (now - timestamp < logoExpiration) {
      console.log(`Using cached logo URL for ${cacheKey}:`, cachedLogoUrl);
      return { logoUrl: cachedLogoUrl, isLoading: false };
    } else {
      console.log(`Cached logo URL for ${cacheKey} expired, fetching new one`);
    }
  }

  // If no config or no logo in config, return empty URL
  if (!config || !config.LOGO) {
    return { logoUrl: '', isLoading: false };
  }

  console.log(`Logo filename from config for ${cacheKey}:`, config.LOGO);

  // Check if the logo URL is absolute or relative
  if (config.LOGO.startsWith('http')) {
    console.log(`Using absolute URL for logo (${cacheKey})`);

    // Cache the logo URL
    localStorage.setItem(cacheKey, config.LOGO);
    localStorage.setItem(`${cacheKey}Timestamp`, Date.now().toString());

    return { logoUrl: config.LOGO, isLoading: false };
  } else {
    console.log(`Fetching presigned URL for logo (${cacheKey})`);

    // Fetch presigned URL for the logo
    const presignedUrl = await fetchLogoPresignedUrl(config.LOGO);
    if (presignedUrl) {
      console.log(`Using presigned URL for logo (${cacheKey}):`, presignedUrl);

      // Cache the logo URL
      localStorage.setItem(cacheKey, presignedUrl);
      localStorage.setItem(`${cacheKey}Timestamp`, Date.now().toString());

      return { logoUrl: presignedUrl, isLoading: false };
    } else {
      console.log(`Using fallback direct URL for logo (${cacheKey})`);

      // Fallback to direct URL if presigned URL fails
      const directUrl = `https://admin.client-api.acuizen.com/files/${config.LOGO}`;

      // Cache the logo URL
      localStorage.setItem(cacheKey, directUrl);
      localStorage.setItem(`${cacheKey}Timestamp`, Date.now().toString());

      return { logoUrl: directUrl, isLoading: false };
    }
  }
};
