
import { Card } from "@/components/ui/card";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from "recharts";
import { Users, Layout, Activity } from "lucide-react";
import { useEffect, useState } from "react";
import { fetchUsers } from "@/services/userService";
import { getModules } from "@/services/moduleService";
import type { User } from "@/services/userService";
import type { Module } from "@/services/moduleService";

const Index = () => {
  const [activeUserCount, setActiveUserCount] = useState<number>(0);
  const [moduleCount, setModuleCount] = useState<number>(0);
  const [totalModules, setTotalModules] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Fetch dashboard data on component mount
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // Fetch users and count active ones
        const users = await fetchUsers();
        const activeUsers = users.filter((user: User) => user.status === 'active' && !user.blocked);
        setActiveUserCount(activeUsers.length);

        // Fetch modules and count active ones
        const modules = await getModules();
        const activeModules = modules.filter((module: Module) => module.status === 'active');
        setModuleCount(activeModules.length);
        setTotalModules(modules.length);

      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        // Set fallback values in case of error
        setActiveUserCount(0);
        setModuleCount(0);
        setTotalModules(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const activityData = [
    { name: "Mon", value: 28 },
    { name: "Tue", value: 35 },
    { name: "Wed", value: 42 },
    { name: "Thu", value: 38 },
    { name: "Fri", value: 45 },
    { name: "Sat", value: 20 },
    { name: "Sun", value: 15 },
  ];

  const moduleUsageData = [
    { name: "Risk Assessment", value: 45 },
    { name: "Observations", value: 60 },
    { name: "Tasks", value: 35 },
    { name: "e-Permits", value: 25 },
    { name: "Incidents", value: 15 },
    { name: "Knowledge", value: 30 },
    { name: "Inspections", value: 20 },
  ];

  const COLORS = ['#FF6B6B', '#4ECDC4', '#FFD166', '#06D6A0', '#F72585', '#7209B7', '#4361EE'];

  const recentActivities = [
    { id: 1, user: "Alex Johnson", action: "Updated User Profile", timestamp: "Today 10:45 AM", module: "User Management" },
    { id: 2, user: "Sarah Williams", action: "Created Risk Assessment", timestamp: "Today 09:30 AM", module: "Risk Assessment" },
    { id: 3, user: "Michael Brown", action: "Completed Task #2345", timestamp: "Yesterday 04:15 PM", module: "Tasks" },
    { id: 4, user: "Emily Davis", action: "Reported Observation #892", timestamp: "Yesterday 02:20 PM", module: "Observations" },
    { id: 5, user: "Robert Wilson", action: "Modified Entity Hierarchy", timestamp: "Aug 25, 2023", module: "Configuration" },
  ];

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader 
          title="Dashboard" 
          subtitle="Welcome to Enterprise Admin Portal" 
        />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <Card className="dashboard-card flex items-center">
            <div className="p-2 bg-blue-100 rounded-full mr-4">
              <Users className="text-blue-500" size={24} />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Active Users</p>
              <h3 className="text-2xl font-bold">
                {isLoading ? "..." : activeUserCount.toLocaleString()}
              </h3>
            </div>
          </Card>
          
          <Card className="dashboard-card flex items-center">
            <div className="p-2 bg-green-100 rounded-full mr-4">
              <Layout className="text-green-500" size={24} />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Subscribed Modules</p>
              <h3 className="text-2xl font-bold">
                {isLoading ? "..." : `${moduleCount}/${totalModules}`}
              </h3>
            </div>
          </Card>
          
          <Card className="dashboard-card flex items-center">
            <div className="p-2 bg-amber-100 rounded-full mr-4">
              <Activity className="text-amber-500" size={24} />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Today's Activity</p>
              <h3 className="text-2xl font-bold">89</h3>
            </div>
          </Card>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <Card className="dashboard-card">
            <h3 className="text-lg font-semibold mb-4">Recent Activity Trends</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={activityData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#4361EE" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card>
          
          <Card className="dashboard-card">
            <h3 className="text-lg font-semibold mb-4">Module Usage Distribution</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={moduleUsageData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {moduleUsageData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </div>
        
        <Card className="dashboard-card">
          <h3 className="text-lg font-semibold mb-4">Recent Activity Log</h3>
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead>
                <tr className="border-b">
                  <th className="pb-3 font-medium">User</th>
                  <th className="pb-3 font-medium">Action</th>
                  <th className="pb-3 font-medium">Module</th>
                  <th className="pb-3 font-medium">Time</th>
                </tr>
              </thead>
              <tbody>
                {recentActivities.map((activity) => (
                  <tr key={activity.id} className="border-b hover:bg-muted/50">
                    <td className="py-3">{activity.user}</td>
                    <td className="py-3">{activity.action}</td>
                    <td className="py-3">{activity.module}</td>
                    <td className="py-3 text-muted-foreground">{activity.timestamp}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      </div>
    </MainLayout>
  );
};

export default Index;
