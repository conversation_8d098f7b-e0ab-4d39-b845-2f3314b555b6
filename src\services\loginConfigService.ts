/**
 * Login Configuration Service
 *
 * Fetches login configuration from the API
 */

import axios from 'axios';
import fallbackConfig from '@/data/loginConfig.json';
import storageService from './storageService';

// Define a type for the fallback config that includes our new properties
type FallbackConfig = {
  LOGO?: string;
  LOGIN_BUTTON_TEXT?: string;
  COMPANY_NAME?: string;
  COMPANY_DESCRIPTION?: string;
  COGNITO_REGION?: string;
  COGNITO_ADMIN_DOMAIN?: string;
  COGNITO_ADMIN_USER_POOL_ID?: string;
  COGNITO_ADMIN_APP_CLIENT_ID?: string;
};

// Cast the imported JSON to our type
const typedFallbackConfig = fallbackConfig as FallbackConfig;

// Login configuration interface
export interface LoginConfig {
  LOGO: string;
  LOGIN_BUTTON_TEXT: string;
  COMPANY_NAME?: string;
  COMPANY_DESCRIPTION?: string;
  COGNITO_REGION: string;
  COGNITO_ADMIN_DOMAIN: string;
  COGNITO_ADMIN_USER_POOL_ID: string;
  COGNITO_ADMIN_APP_CLIENT_ID: string;
}

// Default configuration (fallback)
export const defaultConfig: LoginConfig = {
  LOGO: typedFallbackConfig.LOGO || '',
  LOGIN_BUTTON_TEXT: typedFallbackConfig.LOGIN_BUTTON_TEXT || 'Internal Login',
  COMPANY_NAME: typedFallbackConfig.COMPANY_NAME || 'AcuiZen WorkHub',
  COMPANY_DESCRIPTION: typedFallbackConfig.COMPANY_DESCRIPTION || '',
  COGNITO_REGION: import.meta.env.VITE_AWS_REGION || typedFallbackConfig.COGNITO_REGION || 'ap-southeast-1',
  COGNITO_ADMIN_DOMAIN: import.meta.env.VITE_AWS_OAUTH_DOMAIN || typedFallbackConfig.COGNITO_ADMIN_DOMAIN || 'https://internal-az-admin.auth.ap-southeast-1.amazoncognito.com',
  COGNITO_ADMIN_USER_POOL_ID: import.meta.env.VITE_AWS_USER_POOL_ID || typedFallbackConfig.COGNITO_ADMIN_USER_POOL_ID || 'ap-southeast-1_EZYTtuecq',
  COGNITO_ADMIN_APP_CLIENT_ID: import.meta.env.VITE_AWS_USER_POOL_WEB_CLIENT_ID || typedFallbackConfig.COGNITO_ADMIN_APP_CLIENT_ID || 'j521s697rsnianvpuvgol46ts'
};

// API URL for login configuration
const LOGIN_CONFIG_API_URL = import.meta.env.VITE_LOGIN_CONFIG_API_URL || 'https://admin.client-api.acuizen.com/login-configs';

// CORS proxy URL (if needed)
const CORS_PROXY_URL = import.meta.env.VITE_CORS_PROXY_URL || '';

/**
 * Fetch login configuration from the API
 */
export const fetchLoginConfig = async (): Promise<LoginConfig> => {
  try {
    // Use CORS proxy if provided
    const apiUrl = CORS_PROXY_URL ? `${CORS_PROXY_URL}${LOGIN_CONFIG_API_URL}` : LOGIN_CONFIG_API_URL;
    console.log('Fetching login configuration from:', apiUrl);

    // Get access token from localStorage
    const accessToken = localStorage.getItem('access_token');

    const response = await axios.get<LoginConfig>(apiUrl, {
      timeout: 5000, // 5 second timeout
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {})
      }
    });

    console.log('Login configuration fetched successfully:', response.data);

    // Ensure all required fields are present
    const config = {
      ...defaultConfig,
      ...response.data
    };

    // Make sure we have a login button text
    if (!config.LOGIN_BUTTON_TEXT) {
      config.LOGIN_BUTTON_TEXT = 'Internal Login';
    }

    return config;
  } catch (error) {
    console.error('Failed to fetch login configuration:', error);
    // Log more detailed error information
    if (axios.isAxiosError(error)) {
      console.error('Axios error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
    }

    return defaultConfig;
  }
};

// Storage keys
const STORAGE_KEY_LOGIN_CONFIG = 'login_config';

/**
 * Get login configuration (from cache or API)
 */
export const getLoginConfig = async (): Promise<LoginConfig> => {
  // Check if we have cached config
  const cachedConfig = storageService.getItem<LoginConfig>(STORAGE_KEY_LOGIN_CONFIG);

  if (cachedConfig) {
    console.log('Using cached login configuration');
    return cachedConfig;
  }

  // Fetch from API if no cache
  console.log('No cached login configuration found, fetching from API');
  const config = await fetchLoginConfig();

  // Cache the config with 1 hour expiration
  storageService.setItem(STORAGE_KEY_LOGIN_CONFIG, config, storageService.DEFAULT_EXPIRY);
  console.log('Login configuration cached');

  return config;
};

export default {
  fetchLoginConfig,
  getLoginConfig,
  defaultConfig
};
