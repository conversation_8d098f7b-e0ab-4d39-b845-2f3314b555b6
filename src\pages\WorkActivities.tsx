import { useState, useRef, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Check,
  X,
  Loader2
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchWorkActivities,
  createWorkActivity,
  updateWorkActivity,
  deleteWorkActivity,
} from "@/store/slices/workActivitySlice";
import { WorkActivity } from "@/services/workActivityService";

const WorkActivities = () => {
  const dispatch = useAppDispatch();
  const { workActivities, isLoading, error } = useAppSelector((state) => state.workActivities);
  const [searchTerm, setSearchTerm] = useState("");

  // State for editing
  const [editingActivityId, setEditingActivityId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>("");
  const editInputRef = useRef<HTMLInputElement>(null);

  // State for dialogs
  const [addActivityDialogOpen, setAddActivityDialogOpen] = useState(false);
  const [newActivityName, setNewActivityName] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [activityToDelete, setActivityToDelete] = useState<WorkActivity | null>(null);

  // Fetch work activities on component mount
  useEffect(() => {
    dispatch(fetchWorkActivities());
  }, [dispatch]);

  // Activity editing functions
  const handleStartEditActivity = (activityId: string, name: string) => {
    setEditingActivityId(activityId);
    setEditValue(name);

    // Focus the input field after it's rendered
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus();
      }
    }, 10);
  };

  const handleSaveEditActivity = (activityId: string) => {
    if (!editValue.trim()) {
      setEditingActivityId(null);
      setEditValue("");
      return;
    }

    // Dispatch update action
    dispatch(updateWorkActivity({ 
      activityId, 
      name: editValue.trim() 
    }));

    setEditingActivityId(null);
    setEditValue("");
  };

  const handleCancelEditActivity = () => {
    setEditingActivityId(null);
    setEditValue("");
  };

  // Delete activity functions
  const handleConfirmDeleteActivity = (activity: WorkActivity) => {
    setActivityToDelete(activity);
    setDeleteDialogOpen(true);
  };

  const handleDeleteActivity = () => {
    if (!activityToDelete) return;

    // Dispatch delete action
    dispatch(deleteWorkActivity(activityToDelete.id));

    setDeleteDialogOpen(false);
    setActivityToDelete(null);
  };

  // Add new activity function
  const handleAddActivity = () => {
    if (!newActivityName.trim()) {
      setAddActivityDialogOpen(false);
      setNewActivityName("");
      return;
    }

    // Dispatch create action
    dispatch(createWorkActivity(newActivityName.trim()));

    setAddActivityDialogOpen(false);
    setNewActivityName("");
  };

  const filteredActivities = workActivities.filter((activity) =>
    activity.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title="Work Activities"
          subtitle="Manage work activities across your organization"
          actions={
            <Button onClick={() => {
              setAddActivityDialogOpen(true);
              setNewActivityName("");
            }}>
              <Plus className="mr-2 h-4 w-4" />
              Add Work Activity
            </Button>
          }
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="pt-6">
                <div className="relative mb-6">
                  <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search work activities..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9"
                  />
                </div>

                {isLoading && (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2 text-muted-foreground">Loading work activities...</span>
                  </div>
                )}

                {error && (
                  <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-6">
                    <p className="font-medium">Error</p>
                    <p>{error}</p>
                  </div>
                )}

                {filteredActivities.length > 0 ? (
                  <div className="space-y-2">
                    {filteredActivities.map((activity) => (
                      <div key={activity.id} className="border rounded-md overflow-hidden">
                        <div className="flex items-center justify-between p-3 bg-muted/30">
                          {editingActivityId === activity.id ? (
                            <div className="flex-1 flex items-center">
                              <Input
                                ref={editInputRef}
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className="mr-2"
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    handleSaveEditActivity(activity.id);
                                  } else if (e.key === 'Escape') {
                                    handleCancelEditActivity();
                                  }
                                }}
                              />
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleSaveEditActivity(activity.id)}
                                className="h-8 w-8 p-0 mr-1"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={handleCancelEditActivity}
                                className="h-8 w-8 p-0"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <>
                              <span className="font-medium">{activity.name}</span>
                              <div className="flex items-center">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleStartEditActivity(activity.id, activity.name);
                                  }}
                                  className="h-8 w-8 p-0 mr-1"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleConfirmDeleteActivity(activity);
                                  }}
                                  className="h-8 w-8 p-0 text-destructive"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    {searchTerm ? (
                      <p>No work activities found matching "{searchTerm}"</p>
                    ) : (
                      <p>No work activities found. Add one to get started.</p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="lg:col-span-1">
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-semibold mb-2">About Work Activities</h3>
                <p className="text-muted-foreground mb-4">
                  Work activities represent specific tasks or processes performed within your organization.
                </p>
                <p className="text-muted-foreground mb-4">
                  Use this page to manage all work activities across your organization.
                </p>
                <p className="text-muted-foreground">
                  You can also manage work activities within specific work areas from the Work Areas page.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the work activity "{activityToDelete?.name}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteActivity}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Add Work Activity Dialog */}
      <Dialog open={addActivityDialogOpen} onOpenChange={setAddActivityDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Work Activity</DialogTitle>
            <DialogDescription>
              Create a new work activity for your organization.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="activity-name" className="text-right col-span-1">
                Name
              </label>
              <div className="col-span-3">
                <Input
                  id="activity-name"
                  value={newActivityName}
                  onChange={(e) => setNewActivityName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleAddActivity();
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button onClick={handleAddActivity}>Add Work Activity</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
};

export default WorkActivities;
