
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";

interface Language {
  id: string;
  name: string;
  code: string;
  enabled: boolean;
  isDefault: boolean;
}

const Languages = () => {
  const { t } = useTranslation();
  const {
    languages,
    setDefaultLanguage: setDefaultLang,
    toggleLanguageEnabled,
    saveLanguageSettings
  } = useLanguage();

  // Get the ID of the default language
  const defaultLanguage = languages.find(lang => lang.isDefault)?.id || "1";

  const handleToggle = (id: string) => {
    const success = toggleLanguageEnabled(id);
    if (!success) {
      toast({
        title: t("languages.cannotDisableDefault"),
        description: t("languages.changeDefaultFirst"),
        variant: "destructive",
      });
    }
  };

  const handleDefaultChange = (id: string) => {
    // Make sure the language is enabled before setting as default
    if (!languages.find(lang => lang.id === id)?.enabled) {
      toast({
        title: t("languages.cannotSetAsDefault"),
        description: t("languages.enableFirst"),
        variant: "destructive",
      });
      return;
    }

    setDefaultLang(id);
  };

  const handleSave = () => {
    saveLanguageSettings();
    toast({
      title: t("languages.settingsSaved"),
      description: t("languages.settingsSavedDesc"),
    });
  };

  // Function to reset language settings to defaults
  const handleReset = () => {
    // Clear the saved languages from localStorage
    localStorage.removeItem('appLanguages');
    // Reload the page to apply default settings
    window.location.reload();
  };

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title={t("languages.title")}
          subtitle={t("languages.subtitle")}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left font-medium pb-3">{t("languages.language")}</th>
                        <th className="text-left font-medium pb-3">{t("languages.code")}</th>
                        <th className="text-center font-medium pb-3">{t("languages.status")}</th>
                        <th className="text-center font-medium pb-3">{t("languages.default")}</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {languages.map((language) => (
                        <tr key={language.id} className="hover:bg-muted/30">
                          <td className="py-4">{language.name}</td>
                          <td className="py-4">
                            <Badge variant="outline">{language.code}</Badge>
                          </td>
                          <td className="py-4 text-center">
                            <div className="flex justify-center">
                              <Switch
                                checked={language.enabled}
                                onCheckedChange={() => handleToggle(language.id)}
                              />
                            </div>
                          </td>
                          <td className="py-4 text-center">
                            <div className="flex justify-center">
                              <RadioGroup value={defaultLanguage} onValueChange={handleDefaultChange}>
                                <RadioGroupItem value={language.id} id={`default-${language.id}`} />
                              </RadioGroup>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>

                  <div className="flex justify-end">
                    <Button onClick={handleSave}>{t("common.saveChanges")}</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardContent className="pt-6">
              <h3 className="text-lg font-medium mb-4">{t("languages.settings")}</h3>
              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-medium">{t("languages.availableLanguages")}</h4>
                  <p className="text-muted-foreground">
                    {t("languages.availableLanguagesDesc")}
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">{t("languages.defaultLanguage")}</h4>
                  <p className="text-muted-foreground">
                    {t("languages.defaultLanguageDesc")}
                  </p>
                </div>

                <div className="p-3 bg-muted/30 rounded-md">
                  <h4 className="font-medium">{t("languages.note")}</h4>
                  <p className="text-muted-foreground">
                    {t("languages.noteDesc")}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default Languages;
