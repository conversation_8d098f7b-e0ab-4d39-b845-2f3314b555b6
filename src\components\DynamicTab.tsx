import React, { useState, useEffect } from "react";
import ListB<PERSON> from "./form-elements/ListBox";
import { toast } from "@/hooks/use-toast";
import { api } from "@/services/apiService";
import {
  DROPDOWN_PARENT_ID_DROPDOWNITEM,
  DROPDOWNITEM_WITH_ID,
  DROPDOWN_ID_DROPDOWNITEM
} from "@/constants/apiEndpoints";
import { Card, CardContent } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

interface DropdownItem {
  id: string;
  name: string;
  severityLevel?: string;
  [key: string]: any;
}

interface DynamicTabProps {
  item: {
    id: string;
    name: string;
    levels: string[];
    maskName: string;
    maskId: string;
    description: string;
    created: string;
    updated: string;
    serviceId: string;
  };
}

const DynamicTab: React.FC<DynamicTabProps> = ({ item }) => {
  const [tiers, setTiers] = useState<DropdownItem[][]>(Array(item.levels.length).fill([]));
  const [selectedItems, setSelectedItems] = useState<DropdownItem[]>(
    Array(item.levels.length).fill(null).map(() => ({ id: '', name: '' }))
  );
  const [selectedLevels, setSelectedLevels] = useState<boolean[]>(Array(item.levels.length).fill(false));
  const [loading, setLoading] = useState<boolean>(true);

  // We'll use a different layout approach instead of calculating column sizes

  // Dynamic function to fetch data based on the selected level
  const fetchTierData = async (level: number, id: string) => {
    setLoading(true);
    try {
      let endpoint: string;
      if (level === 0) {
        endpoint = DROPDOWN_ID_DROPDOWNITEM(id);
      } else {
        endpoint = DROPDOWN_PARENT_ID_DROPDOWNITEM(id);
      }

      const response = await api.get<DropdownItem[]>(endpoint);

      const newTiers = [...tiers];
      newTiers[level] = response || [];
      setTiers(newTiers);
    } catch (error) {
      console.error(`Failed to fetch tier ${level} data:`, error);
      toast({
        title: "Error",
        description: `Failed to fetch data for ${item.levels[level]}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTierData(0, item.id);
    setSelectedLevels([true, ...Array(item.levels.length - 1).fill(false)]); // Default first level selected
  }, [item, item.id]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleSelect = (level: number, id: string) => {
    const newSelectedItems = [...selectedItems];
    const foundItem = tiers[level].find(i => i.id === id);
    newSelectedItems[level] = foundItem || { id: '', name: '' };
    setSelectedItems(newSelectedItems);

    // Mark the current and next level as selected
    const newSelectedLevels = [...selectedLevels];
    newSelectedLevels[level] = true;

    // Reset all levels after the selected one
    for (let i = level + 1; i < item.levels.length; i++) {
      newSelectedLevels[i] = i === level + 1; // Only the next level should be true
      newSelectedItems[i] = { id: '', name: '' };
    }

    setSelectedLevels(newSelectedLevels);

    // Fetch data for the next level if it exists
    if (level < item.levels.length - 1) {
      fetchTierData(level + 1, id);
    }
  };

  const createItem = async (level: number, value: string) => {
    try {
      const endpoint = level === 0
        ? DROPDOWN_ID_DROPDOWNITEM(item.id)
        : DROPDOWN_PARENT_ID_DROPDOWNITEM(selectedItems[level - 1].id);

      const response = await api.post<DropdownItem>(endpoint, { name: value });

      if (response && response.id) {
        const newTiers = [...tiers];
        newTiers[level] = [...newTiers[level], response];
        setTiers(newTiers);

        toast({
          title: "Success",
          description: "Item created successfully",
        });
      }
    } catch (error) {
      console.error("Failed to create item:", error);
      toast({
        title: "Error",
        description: "Failed to create item",
        variant: "destructive",
      });
    }
  };

  const handleDeleteItem = async (_mode: string, id: string, level: number) => {
    try {
      const endpoint = DROPDOWNITEM_WITH_ID(id);

      await api.delete(endpoint);

      const newTiers = [...tiers];
      newTiers[level] = newTiers[level].filter(i => i.id !== id);
      setTiers(newTiers);

      // Reset selections for this level and lower levels
      const newSelectedItems = [...selectedItems];
      const newSelectedLevels = [...selectedLevels];

      for (let i = level; i < selectedItems.length; i++) {
        if (i > level) {
          newSelectedLevels[i] = false;
          newTiers[i] = [];
        }
        newSelectedItems[i] = { id: '', name: '' };
      }

      setSelectedItems(newSelectedItems);
      setSelectedLevels(newSelectedLevels);

      toast({
        title: "Success",
        description: "Item deleted successfully",
      });
    } catch (error) {
      console.error("Failed to delete item:", error);
      toast({
        title: "Error",
        description: "Failed to delete item",
        variant: "destructive",
      });
    }
  };

  const handleEditItem = async (id: string, name: string, level: number) => {
    try {
      const endpoint = DROPDOWNITEM_WITH_ID(id);

      await api.patch(endpoint, { name });

      // Update the item in the local state
      const newTiers = [...tiers];
      const itemIndex = newTiers[level].findIndex(i => i.id === id);

      if (itemIndex !== -1) {
        newTiers[level][itemIndex] = {
          ...newTiers[level][itemIndex],
          name
        };
        setTiers(newTiers);
      }

      toast({
        title: "Success",
        description: "Item updated successfully",
      });
    } catch (error) {
      console.error("Failed to update item:", error);
      toast({
        title: "Error",
        description: "Failed to update item",
        variant: "destructive",
      });
    }
  };

  interface DescriptionData {
    severityLevel: string;
  }

  const handleAddDescription = async (id: string, data: DescriptionData) => {
    try {
      const endpoint = DROPDOWNITEM_WITH_ID(id);

      await api.patch(endpoint, { severityLevel: data.severityLevel });

      toast({
        title: "Success",
        description: "Description updated successfully",
      });
    } catch (error) {
      console.error("Failed to update description:", error);
      toast({
        title: "Error",
        description: "Failed to update description",
        variant: "destructive",
      });
    }
  };

  // Determine if we should use tabs or columns based on the number of levels
  const useTabs = item.levels.length > 3;

  return (
    <div className="w-full">
      <Card>
        <CardContent className="pt-6">
          {loading && tiers[0].length === 0 ? (
            <div className="flex justify-center items-center h-40">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : useTabs ? (
            // Tabs layout for more than 3 levels
            <div className="flex flex-col">
              <div className="flex border-b mb-4">
                {item.levels.map((level, i) => (
                  <button
                    key={i}
                    className={`px-4 py-2 font-medium text-sm ${
                      selectedLevels[i]
                        ? 'border-b-2 border-primary text-primary'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => {
                      // Make this level active if it's the first level or if the previous level has a selection
                      if (i === 0 || (i > 0 && selectedItems[i-1].id !== '')) {
                        const newSelectedLevels = [...selectedLevels];
                        newSelectedLevels[i] = true;
                        // Set all other levels to false
                        for (let j = 0; j < newSelectedLevels.length; j++) {
                          if (j !== i) newSelectedLevels[j] = false;
                        }
                        setSelectedLevels(newSelectedLevels);
                      }
                    }}
                    disabled={i > 0 && !selectedItems[i-1].id}
                  >
                    {level}
                  </button>
                ))}
              </div>

              {/* Show only the active level */}
              {item.levels.map((level, i) => (
                selectedLevels[i] && (
                  <div key={i} className="w-full">
                    <ListBox
                      handleDeleteItem={(mode, id) => handleDeleteItem(mode, id, i)}
                      handleEditItem={(id, name) => handleEditItem(id, name, i)}
                      selected={true} // Always selected in tab view
                      title={level}
                      onHandleCreateItem={(value) => createItem(i, value)}
                      lists={tiers[i]}
                      handleSelect={(id) => handleSelect(i, id)}
                      selectedItem={selectedItems[i]}
                      mode={'tier' + (i + 1)}
                      handleAddDescription={handleAddDescription}
                    />
                  </div>
                )
              ))}
            </div>
          ) : (
            // Responsive grid layout for 3 or fewer levels
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {item.levels.map((level, i) => (
                <div key={i} className={`${item.levels.length === 1 ? 'col-span-full' : ''}`}>
                  <ListBox
                    handleDeleteItem={(mode, id) => handleDeleteItem(mode, id, i)}
                    handleEditItem={(id, name) => handleEditItem(id, name, i)}
                    selected={selectedLevels[i]}
                    title={level}
                    onHandleCreateItem={(value) => createItem(i, value)}
                    lists={tiers[i]}
                    handleSelect={(id) => handleSelect(i, id)}
                    selectedItem={selectedItems[i]}
                    mode={'tier' + (i + 1)}
                    handleAddDescription={handleAddDescription}
                  />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DynamicTab;
