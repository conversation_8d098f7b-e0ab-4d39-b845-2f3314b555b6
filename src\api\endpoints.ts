/**
 * API Endpoints Configuration
 * 
 * Centralizes all API endpoints and base URLs for different environments.
 */

// Environment types
export type Environment = 'development' | 'staging' | 'production';

// Get current environment from env variables or default to development
export const getCurrentEnvironment = (): Environment => {
  const env = import.meta.env.VITE_APP_ENV || 'development';
  return env as Environment;
};

// Base URLs for different environments
const BASE_URLS: Record<Environment, string> = {
  development: 'http://localhost:8000/api',
  staging: 'https://staging-api.example.com/api',
  production: 'https://api.example.com/api',
};

// Get the base URL for the current environment
export const getBaseUrl = (): string => {
  const environment = getCurrentEnvironment();
  return BASE_URLS[environment];
};

// API version
export const API_VERSION = 'v1';

// Authentication endpoints
export const AUTH_ENDPOINTS = {
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  REFRESH_TOKEN: '/auth/refresh-token',
  LOGOUT: '/auth/logout',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
};

// User endpoints
export const USER_ENDPOINTS = {
  PROFILE: '/users/profile',
  UPDATE_PROFILE: '/users/profile',
  CHANGE_PASSWORD: '/users/change-password',
  LIST: '/users',
  DETAILS: (id: string | number) => `/users/${id}`,
  CREATE: '/users',
  UPDATE: (id: string | number) => `/users/${id}`,
  DELETE: (id: string | number) => `/users/${id}`,
};

// Module endpoints
export const MODULE_ENDPOINTS = {
  LIST: '/modules',
  DETAILS: (id: string | number) => `/modules/${id}`,
  TOGGLE_STATUS: (id: string | number) => `/modules/${id}/toggle-status`,
};

// Department endpoints
export const DEPARTMENT_ENDPOINTS = {
  LIST: '/departments',
  DETAILS: (id: string | number) => `/departments/${id}`,
  CREATE: '/departments',
  UPDATE: (id: string | number) => `/departments/${id}`,
  DELETE: (id: string | number) => `/departments/${id}`,
};

// Entity hierarchy endpoints
export const ENTITY_ENDPOINTS = {
  LIST: '/entities',
  TREE: '/entities/tree',
  DETAILS: (id: string | number) => `/entities/${id}`,
  CREATE: '/entities',
  UPDATE: (id: string | number) => `/entities/${id}`,
  DELETE: (id: string | number) => `/entities/${id}`,
};

// Activity log endpoints
export const ACTIVITY_ENDPOINTS = {
  LIST: '/activity-logs',
  DETAILS: (id: string | number) => `/activity-logs/${id}`,
};

// Settings endpoints
export const SETTINGS_ENDPOINTS = {
  GET: '/settings',
  UPDATE: '/settings',
};

// Language endpoints
export const LANGUAGE_ENDPOINTS = {
  LIST: '/languages',
  DETAILS: (id: string | number) => `/languages/${id}`,
  UPDATE: (id: string | number) => `/languages/${id}`,
  TOGGLE_STATUS: (id: string | number) => `/languages/${id}/toggle-status`,
  SET_DEFAULT: (id: string | number) => `/languages/${id}/set-default`,
};

// Export all endpoints
export default {
  BASE_URLS,
  getBaseUrl,
  API_VERSION,
  AUTH: AUTH_ENDPOINTS,
  USER: USER_ENDPOINTS,
  MODULE: MODULE_ENDPOINTS,
  DEPARTMENT: DEPARTMENT_ENDPOINTS,
  ENTITY: ENTITY_ENDPOINTS,
  ACTIVITY: ACTIVITY_ENDPOINTS,
  SETTINGS: SETTINGS_ENDPOINTS,
  LANGUAGE: LANGUAGE_ENDPOINTS,
};
