# Drag and Drop Feature for Entity Management

## Overview
Added drag-and-drop functionality to the Entity Hierarchy management UI, allowing users to reorder entities within each tier/column by dragging them vertically.

## Features Implemented

### 1. Drag and Drop Functionality
- **Vertical Reordering**: Users can drag and drop items up and down within each column (tier)
- **Multi-tier Support**: Works for all hierarchy levels - Country, Region, Site, Level, and Zone
- **Visual Feedback**: 
  - Drag handle appears on hover/selection
  - Visual indicators during drag operations
  - Smooth animations and transitions

### 2. Technical Implementation
- **Library**: Uses `@dnd-kit` for modern, accessible drag-and-drop
- **Components**: 
  - `DndContext` wraps the entire entity management section
  - `SortableContext` for each column with vertical list strategy
  - `SortableEntityCard` component for draggable items
  - `DragOverlay` for visual feedback during drag

### 3. User Experience
- **Drag Handle**: Grip icon appears on hover and when item is selected
- **Visual States**: 
  - Dragging items become semi-transparent
  - Drop zones are clearly indicated
  - Smooth animations for reordering
- **Accessibility**: Keyboard navigation support included

### 4. Backend Integration
- **API Endpoint**: `PATCH /api/{level}/reorder` (to be implemented)
- **Payload**: Array of entity IDs in new order + parent ID
- **Fallback**: Local reordering works even if backend endpoint is not available
- **Error Handling**: Reverts changes if API call fails

## Usage Instructions

### For Users:
1. Navigate to Entity Hierarchy page
2. Hover over any entity card to see the drag handle (grip icon)
3. Click and drag the handle to move the item up or down within the same column
4. Release to drop the item in the new position
5. The system will automatically save the new order

### For Developers:
1. The drag-and-drop is implemented in `src/pages/EntityHierarchy.tsx`
2. Backend API integration is in `src/services/locationService.ts`
3. The `reorderLocations` function handles the API calls
4. Currently returns success even if backend endpoint doesn't exist (graceful degradation)

## Files Modified

### 1. `src/pages/EntityHierarchy.tsx`
- Added @dnd-kit imports and components
- Implemented drag-and-drop state management
- Created `SortableEntityCard` component
- Added drag handlers (`handleDragStart`, `handleDragEnd`)
- Enhanced `EntityCard` with drag handle and visual states
- Wrapped entity lists with `SortableContext`
- Added `DragOverlay` for better UX

### 2. `src/services/locationService.ts`
- Added `reorderLocations` function for backend API calls
- Handles cache invalidation after reordering
- Graceful fallback if backend endpoint is not implemented

## Dependencies
- `@dnd-kit/core`: Core drag-and-drop functionality
- `@dnd-kit/sortable`: Sortable list implementation
- `@dnd-kit/utilities`: Utility functions for transforms
- All dependencies were already installed in the project

## Future Enhancements
1. **Cross-column Dragging**: Allow moving items between different hierarchy levels
2. **Batch Operations**: Select multiple items and move them together
3. **Undo/Redo**: Add ability to undo reordering operations
4. **Drag Indicators**: Show insertion point indicators during drag
5. **Touch Support**: Enhanced mobile/touch device support

## Testing
- Test drag-and-drop functionality on the Entity Hierarchy page
- Verify visual feedback during drag operations
- Confirm that reordering persists (locally for now)
- Test keyboard accessibility
- Verify error handling when API calls fail

## Notes
- The backend API endpoint for reordering is not yet implemented
- Currently works with local state management
- Graceful degradation ensures functionality works regardless of backend status
- All existing functionality (add, edit, delete) remains unchanged
