/**
 * Token Helper Utility
 *
 * This utility provides functions to help with token management.
 * It can be used to manually set the token in Redux and localStorage.
 */

import { setAuthToken } from '@/services/apiService';
import { store } from '@/store';

/**
 * Set the authentication token manually
 * This function can be called from the browser console to set the token
 *
 * @param token The authentication token to set
 */
export const setToken = (token: string): void => {
  if (!token) {
    console.error('Token is required');
    return;
  }

  try {
    // Set the token in Redux and localStorage
    setAuthToken(token);

    console.log('Token set successfully');
    console.log('Current Redux state:', store.getState().auth);
  } catch (error) {
    console.error('Error setting token:', error);
  }
};

/**
 * Get the current authentication token from Redux
 * This function can be called from the browser console to get the token
 */
export const getToken = (): string | null => {
  try {
    const state = store.getState();
    return state.auth.token;
  } catch (error) {
    console.error('Error getting token:', error);
    return null;
  }
};

/**
 * Log the current authentication state
 * This function can be called from the browser console to log the auth state
 */
export const logAuthState = (): void => {
  try {
    const state = store.getState();
    console.log('Current Redux auth state:', state.auth);

    // Check localStorage for persist:auth
    const persistedAuth = localStorage.getItem('persist:auth');
    if (persistedAuth) {
      console.log('Persisted auth state found in localStorage');
      const parsedAuth = JSON.parse(persistedAuth);
      console.log('Parsed persisted auth:', parsedAuth);

      if (parsedAuth.token) {
        const token = JSON.parse(parsedAuth.token);
        console.log('Token from persisted auth:', token ? `Present (${token.substring(0, 10)}...)` : 'Missing');
      }
    } else {
      console.log('No persisted auth state found in localStorage');
    }

    // Check for Cognito tokens
    const cognitoKeys = Object.keys(localStorage).filter(key =>
      key.startsWith('CognitoIdentityServiceProvider.')
    );
    console.log('Cognito keys in localStorage:', cognitoKeys);

    // Check for access_token
    const accessToken = localStorage.getItem('access_token');
    console.log('access_token in localStorage:', accessToken ? `Present (${accessToken.substring(0, 10)}...)` : 'Missing');
  } catch (error) {
    console.error('Error logging auth state:', error);
  }
};

/**
 * Make a direct request to the OAuth2 token endpoint
 * This function can be called from the browser console to get tokens
 *
 * @param domain The Cognito domain (e.g., https://your-domain.auth.us-east-1.amazoncognito.com)
 * @param clientId The Cognito app client ID
 * @param grantType The grant type (e.g., 'authorization_code', 'refresh_token', 'password')
 * @param params Additional parameters based on the grant type
 */
export const requestOAuth2Token = async (
  domain: string,
  clientId: string,
  grantType: 'authorization_code' | 'refresh_token' | 'password',
  params: {
    code?: string;
    redirect_uri?: string;
    refresh_token?: string;
    username?: string;
    password?: string;
  }
): Promise<void> => {
  try {
    console.log(`Making ${grantType} request to OAuth2 token endpoint...`);

    // Prepare the request body
    const body = new URLSearchParams({
      grant_type: grantType,
      client_id: clientId,
      ...params
    });

    console.log('Request parameters:', {
      domain,
      clientId,
      grantType,
      ...params
    });

    // Make the request to the token endpoint
    const response = await fetch(`${domain}/oauth2/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: body.toString()
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to get tokens:', response.status, errorText);
      return;
    }

    const data = await response.json();
    console.log('=== OAuth2 Token Response ===');
    console.log('Response from OAuth2 token endpoint:', data);

    // Log each token separately for debugging
    console.log('Access Token:', data.access_token ? 'Present (length: ' + data.access_token.length + ')' : 'Missing');
    console.log('ID Token:', data.id_token ? 'Present (length: ' + data.id_token.length + ')' : 'Missing');
    console.log('Refresh Token:', data.refresh_token ? 'Present (length: ' + data.refresh_token.length + ')' : 'Missing');
    console.log('Expires In:', data.expires_in || 'Not specified');
    console.log('Token Type:', data.token_type || 'Not specified');
    console.log('=== End OAuth2 Token Response ===');

    // Ask if the user wants to set this token in Redux
    console.log('To set this token in Redux, call:');
    console.log(`tokenHelper.setToken('${data.access_token}')`);
  } catch (error) {
    console.error('Error requesting OAuth2 token:', error);
  }
};

// Expose these functions to the window object for easy access from the console
declare global {
  interface Window {
    tokenHelper: {
      setToken: (token: string) => void;
      getToken: () => string | null;
      logAuthState: () => void;
      requestOAuth2Token: (
        domain: string,
        clientId: string,
        grantType: 'authorization_code' | 'refresh_token' | 'password',
        params: {
          code?: string;
          redirect_uri?: string;
          refresh_token?: string;
          username?: string;
          password?: string;
        }
      ) => Promise<void>;
    };
  }
}

// Only add to window in development mode
if (process.env.NODE_ENV !== 'production') {
  window.tokenHelper = {
    setToken,
    getToken,
    logAuthState,
    requestOAuth2Token
  };
}

export default {
  setToken,
  getToken,
  logAuthState,
  requestOAuth2Token
};
