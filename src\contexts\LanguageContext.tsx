import React, { createContext, useState, useContext, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { AVAILABLE_LANGUAGES, getLanguageCode, isRTL } from '@/lib/i18n';

// Define the Language type
export interface Language {
  id: string;
  name: string;
  code: string;
  enabled: boolean;
  isDefault: boolean;
}

// Define the context type
interface LanguageContextType {
  languages: Language[];
  setLanguages: React.Dispatch<React.SetStateAction<Language[]>>;
  currentLanguage: Language;
  changeLanguage: (langCode: string) => void;
  setDefaultLanguage: (id: string) => void;
  toggleLanguageEnabled: (id: string) => boolean;
  saveLanguageSettings: () => void;
  isRTL: boolean;
}

// Create the context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Create a provider component
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { i18n } = useTranslation();
  const [languages, setLanguages] = useState<Language[]>(AVAILABLE_LANGUAGES);

  // Find the current language based on i18n.language
  const getCurrentLanguage = (): Language => {
    const currentLangCode = i18n.language;
    const langCode = getLanguageCode(currentLangCode);

    // Find the language in our languages array
    // First try to find an exact match
    let lang = languages.find(l => l.code.toLowerCase() === currentLangCode.toLowerCase() && l.enabled);

    // If not found, try to match just the language part
    if (!lang) {
      lang = languages.find(l => getLanguageCode(l.code) === langCode && l.enabled);
    }

    // If still not found or not enabled, return the default language
    if (!lang) {
      return languages.find(l => l.isDefault) || languages[0];
    }

    return lang;
  };

  const [currentLanguage, setCurrentLanguage] = useState<Language>(getCurrentLanguage());

  // Update current language when i18n.language changes
  useEffect(() => {
    setCurrentLanguage(getCurrentLanguage());
  }, [i18n.language, languages]);

  // Change the language
  const changeLanguage = (langCode: string) => {
    // Use the full language code for better locale support
    i18n.changeLanguage(langCode);

    // Log for debugging
    console.log(`Changed language to: ${langCode}`);
  };

  // Set the default language
  const setDefaultLanguage = (id: string) => {
    // Make sure the language is enabled
    const lang = languages.find(l => l.id === id);
    if (!lang || !lang.enabled) {
      return;
    }

    // Update the languages array
    const updatedLanguages = languages.map(l => ({
      ...l,
      isDefault: l.id === id,
    }));

    setLanguages(updatedLanguages);

    // If the current language is not enabled, switch to the new default
    if (!currentLanguage.enabled) {
      changeLanguage(lang.code);
    }
  };

  // Toggle a language's enabled state
  const toggleLanguageEnabled = (id: string): boolean => {
    // Find the language
    const lang = languages.find(l => l.id === id);
    if (!lang) {
      return false;
    }

    // Don't allow disabling the default language
    if (lang.isDefault && lang.enabled) {
      return false;
    }

    // Update the languages array
    const updatedLanguages = languages.map(l => {
      if (l.id === id) {
        return { ...l, enabled: !l.enabled };
      }
      return l;
    });

    setLanguages(updatedLanguages);

    // If we're disabling the current language, switch to the default
    if (lang.code === currentLanguage.code && lang.enabled) {
      const defaultLang = updatedLanguages.find(l => l.isDefault);
      if (defaultLang) {
        changeLanguage(defaultLang.code);
      }
    }

    return true;
  };

  // Save language settings (in a real app, this would call an API)
  const saveLanguageSettings = () => {
    // In a real app, this would save to a backend
    // For now, we'll just save to localStorage
    localStorage.setItem('appLanguages', JSON.stringify(languages));

    // Ensure the current language is set correctly
    const currentLang = getCurrentLanguage();
    changeLanguage(currentLang.code);
  };

  // Load saved languages from localStorage on mount
  useEffect(() => {
    const savedLanguages = localStorage.getItem('appLanguages');
    if (savedLanguages) {
      try {
        const parsedLanguages = JSON.parse(savedLanguages) as Language[];

        // Ensure all languages from AVAILABLE_LANGUAGES are present
        // This is important when new languages are added to the application
        const mergedLanguages = [...AVAILABLE_LANGUAGES];

        // Update existing languages with saved settings
        parsedLanguages.forEach(savedLang => {
          const index = mergedLanguages.findIndex(l => l.id === savedLang.id);
          if (index !== -1) {
            mergedLanguages[index] = {
              ...mergedLanguages[index],
              enabled: savedLang.enabled,
              isDefault: savedLang.isDefault
            };
          }
        });

        setLanguages(mergedLanguages);
        console.log("Merged languages:", mergedLanguages);
      } catch (error) {
        console.error('Failed to parse saved languages', error);
      }
    }
  }, []);

  // Check if current language is RTL
  const rtl = isRTL(currentLanguage.code);

  // Update document direction when language changes
  useEffect(() => {
    document.documentElement.dir = rtl ? 'rtl' : 'ltr';
    document.documentElement.lang = getLanguageCode(currentLanguage.code);
  }, [rtl, currentLanguage.code]);

  return (
    <LanguageContext.Provider
      value={{
        languages,
        setLanguages,
        currentLanguage,
        changeLanguage,
        setDefaultLanguage,
        toggleLanguageEnabled,
        saveLanguageSettings,
        isRTL: rtl,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

// Create a hook to use the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
