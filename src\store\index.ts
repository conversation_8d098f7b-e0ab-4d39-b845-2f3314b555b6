import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import navigationReducer from './slices/navigationSlice';
import authReducer from './slices/authSlice';
import workAreaReducer from './slices/workAreaSlice';
import workActivityReducer from './slices/workActivitySlice';
import userReducer from './slices/userSlice';

// Define the root reducer
const rootReducer = combineReducers({
  navigation: navigationReducer,
  auth: authReducer,
  workAreas: workAreaReducer,
  workActivities: workActivityReducer,
  users: userReducer,
});

// Configure the persistence
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'], // Only persist auth state for now
};

// Create the persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure the store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

// Create the persistor
export const persistor = persistStore(store);

// Define RootState and AppDispatch types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
