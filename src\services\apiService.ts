import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { toast } from '@/components/ui/use-toast';
import storageService from './storageService';
import { store } from '@/store';
import { handleUnauthorized } from './userProfileService';

// Base URL for API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://admin.client-api.acuizen.com';

// Create axios instance with default config
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 15000, // 15 seconds
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Storage keys
const STORAGE_KEY_AUTH_TOKEN = 'auth_token';

// Function to check for Cognito tokens in localStorage
const checkForCognitoTokens = (): string | null => {
  try {
    // Get all localStorage keys
    const keys = Object.keys(localStorage);

    // Find keys that match the Cognito pattern and contain 'accessToken'
    const accessTokenKeys = keys.filter(key =>
      key.startsWith('CognitoIdentityServiceProvider.') &&
      key.endsWith('.accessToken')
    );

    if (accessTokenKeys.length > 0) {
      // Use the first one
      const cognitoAccessToken = localStorage.getItem(accessTokenKeys[0]);

      if (cognitoAccessToken) {
        // Extract the key prefix
        const foundKeyPrefix = accessTokenKeys[0].replace('.accessToken', '');

        // Try to get the other tokens with the same prefix
        const cognitoIdToken = localStorage.getItem(`${foundKeyPrefix}.idToken`);
        const cognitoRefreshToken = localStorage.getItem(`${foundKeyPrefix}.refreshToken`);

        // Store in our standard keys
        localStorage.setItem('access_token', cognitoAccessToken);

        if (cognitoIdToken) {
          localStorage.setItem('id_token', cognitoIdToken);
        }

        if (cognitoRefreshToken) {
          localStorage.setItem('refresh_token', cognitoRefreshToken);
        }

        localStorage.setItem('token_type', 'Bearer');
        localStorage.setItem('expires_in', '3600');

        // Also store in our storage service for backward compatibility
        storageService.setItem(STORAGE_KEY_AUTH_TOKEN, cognitoAccessToken, 3600 * 1000);

        return cognitoAccessToken;
      }
    }

    return null;
  } catch (error) {
    return null;
  }
};



// Request interceptor for API calls
apiClient.interceptors.request.use(
  (config) => {
    // Get the token using our helper function (prioritizes Redux)
    const accessToken = getAccessTokenForRequest();

    // If token exists, add it to the headers
    if (accessToken && config.headers) {
      config.headers['Authorization'] = `Bearer ${accessToken}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for API calls
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle errors based on status code
    if (error.response) {
      const { status } = error.response;

      switch (status) {
        case 401: // Unauthorized
          // Use the centralized handleUnauthorized function
          handleUnauthorized();
          break;

        case 403: // Forbidden
          toast({
            title: 'Access Denied',
            description: 'You do not have permission to perform this action.',
            variant: 'destructive'
          });
          break;

        case 404: // Not Found
          toast({
            title: 'Resource Not Found',
            description: 'The requested resource could not be found.',
            variant: 'destructive'
          });
          break;

        case 500: // Server Error
        case 502: // Bad Gateway
        case 503: // Service Unavailable
        case 504: // Gateway Timeout
          toast({
            title: 'Server Error',
            description: 'An error occurred on the server. Please try again later.',
            variant: 'destructive'
          });
          break;

        default:
          // Handle other errors
          toast({
            title: 'Error',
            description: error.message || 'An unexpected error occurred.',
            variant: 'destructive'
          });
      }
    } else if (error.request) {
      // The request was made but no response was received
      toast({
        title: 'Network Error',
        description: 'Unable to connect to the server. Please check your internet connection.',
        variant: 'destructive'
      });
    } else {
      // Something happened in setting up the request
      toast({
        title: 'Request Error',
        description: error.message || 'An error occurred while setting up the request.',
        variant: 'destructive'
      });
    }

    return Promise.reject(error);
  }
);

// We already imported the store at the top of the file

// Helper function to get the access token (prioritizing Redux)
const getAccessTokenForRequest = (): string | null => {
  try {
    // First try to get the token directly from Redux store
    const reduxState = store.getState();
    if (reduxState.auth && reduxState.auth.token) {
      return reduxState.auth.token;
    }

    // Then try to get the token from Redux persist storage
    const authState = localStorage.getItem('persist:auth');
    if (authState) {
      const parsedAuthState = JSON.parse(authState);
      if (parsedAuthState.token) {
        // Remove quotes if present (localStorage stringifies JSON)
        const token = JSON.parse(parsedAuthState.token);

        if (token) {
          return token;
        }
      }
    }

    // Then try to get the token from our storage service
    const token = storageService.getItem<string>(STORAGE_KEY_AUTH_TOKEN);
    if (token) {
      return token;
    }

    // As a last resort, check for Cognito tokens
    const cognitoToken = checkForCognitoTokens();
    if (cognitoToken) {
      return cognitoToken;
    }

    return null;
  } catch (error) {
    return null;
  }
};

// Function to manually set the token in Redux
export const setAuthToken = (token: string): void => {
  try {
    // Import the action creator
    const { setToken } = require('@/store/slices/authSlice');

    // Dispatch the action to update the token in Redux
    store.dispatch(setToken(token));

    // Also store in storage service for backward compatibility
    storageService.setItem(STORAGE_KEY_AUTH_TOKEN, token, 3600 * 1000);
  } catch (error) {
    // Silent error handling
  }
};

// Helper functions for common API operations
export const api = {
  get: <T>(url: string, config?: AxiosRequestConfig) => {
    // Ensure Authorization header is included
    const accessToken = getAccessTokenForRequest();
    const headers = {
      ...(config?.headers || {}),
      ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {})
    };

    return apiClient.get<T>(url, { ...config, headers }).then(response => response.data);
  },

  post: <T>(url: string, data?: any, config?: AxiosRequestConfig) => {
    // Ensure Authorization header is included
    const accessToken = getAccessTokenForRequest();
    const headers = {
      ...(config?.headers || {}),
      ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {})
    };

    return apiClient.post<T>(url, data, { ...config, headers }).then(response => response.data);
  },

  put: <T>(url: string, data?: any, config?: AxiosRequestConfig) => {
    // Ensure Authorization header is included
    const accessToken = getAccessTokenForRequest();
    const headers = {
      ...(config?.headers || {}),
      ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {})
    };

    return apiClient.put<T>(url, data, { ...config, headers }).then(response => response.data);
  },

  patch: <T>(url: string, data?: any, config?: AxiosRequestConfig) => {
    // Ensure Authorization header is included
    const accessToken = getAccessTokenForRequest();
    const headers = {
      ...(config?.headers || {}),
      ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {})
    };

    return apiClient.patch<T>(url, data, { ...config, headers }).then(response => response.data);
  },

  delete: <T>(url: string, config?: AxiosRequestConfig) => {
    // Ensure Authorization header is included
    const accessToken = getAccessTokenForRequest();
    const headers = {
      ...(config?.headers || {}),
      ...(accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {})
    };

    return apiClient.delete<T>(url, { ...config, headers }).then(response => response.data);
  }
};

export default apiClient;
