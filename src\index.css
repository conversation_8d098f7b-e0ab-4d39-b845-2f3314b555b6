
/* Import RTL styles */
@import './styles/rtl.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* AZ Brand Colors - Blue Primary (#005284) */
    --primary: 200 100% 26.1%; /* AZ Blue */
    --primary-foreground: 210 40% 98%;

    /* AZ Brand Colors - Light Blue Secondary */
    --secondary: 200 85.1% 33.1%; /* AZ Blue Light */
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* AZ Brand Colors - Red Accent (#BE1F24) */
    --accent: 358 72.1% 43.5%; /* AZ Red */
    --accent-foreground: 210 40% 98%;

    --destructive: 358 72.1% 43.5%; /* AZ Red */
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 200 100% 26.1%; /* AZ Blue */

    --radius: 0.5rem;

    /* Sidebar with AZ Blue */
    --sidebar-background: 200 100% 20%; /* Darker AZ Blue */
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 200 100% 26.1%; /* AZ Blue */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 200 100% 15%; /* Even Darker AZ Blue */
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 200 100% 17.5%; /* Darker AZ Blue */
    --sidebar-ring: 200 100% 26.1%; /* AZ Blue */
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .module-card {
    @apply bg-white rounded-lg shadow-md p-6 flex flex-col transition-all duration-200 relative overflow-hidden;
    height: 200px; /* Fixed height for all cards */
  }

  .dashboard-card {
    @apply bg-white rounded-lg shadow-sm p-6 transition-all duration-200 hover:shadow-md;
  }

  .content-container {
    @apply flex-1 overflow-auto p-6 bg-gray-50;
  }
}
