import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  User,
  fetchUsers as fetchUsersAP<PERSON>,
  createUser as createUser<PERSON><PERSON>,
  createUserWithSpecificFormat as createUserWithSpecificFormatAPI,
  updateUser as updateUser<PERSON><PERSON>,
  updateUserWithSpecificUrl as updateUserWithSpecificUrlAPI,
  deleteUser as deleteUser<PERSON><PERSON>,
  resetUserMF<PERSON> as resetUserMFAA<PERSON>
} from '@/services/userService';

// Define the state interface
interface UserState {
  users: User[];
  internalUsers: User[];
  externalUsers: User[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: UserState = {
  users: [],
  internalUsers: [],
  externalUsers: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (_, { rejectWithValue }) => {
    try {
      return await fetchUsersAPI();
    } catch (error) {
      return rejectWithValue('Failed to fetch users');
    }
  }
);

export const createUser = createAsyncThunk(
  'users/createUser',
  async (userData: User, { rejectWithValue }) => {
    try {
      const user = await createUserAPI(userData);
      if (!user) {
        return rejectWithValue('Failed to create user');
      }
      return user;
    } catch (error) {
      return rejectWithValue('Failed to create user');
    }
  }
);

export const createUserWithSpecificFormat = createAsyncThunk(
  'users/createUserWithSpecificFormat',
  async (userData: User, { rejectWithValue }) => {
    try {
      const user = await createUserWithSpecificFormatAPI(userData);
      if (!user) {
        return rejectWithValue('Failed to create user');
      }
      return user;
    } catch (error) {
      return rejectWithValue('Failed to create user');
    }
  }
);

export const updateUser = createAsyncThunk(
  'users/updateUser',
  async ({ id, userData }: { id: string; userData: Partial<User> }, { rejectWithValue }) => {
    try {
      const success = await updateUserAPI(id, userData);
      if (!success) {
        return rejectWithValue('Failed to update user');
      }
      return { id, ...userData };
    } catch (error) {
      return rejectWithValue('Failed to update user');
    }
  }
);

export const updateUserWithSpecificUrl = createAsyncThunk(
  'users/updateUserWithSpecificUrl',
  async ({ id, userData }: { id: string; userData: Partial<User> }, { rejectWithValue }) => {
    try {
      const success = await updateUserWithSpecificUrlAPI(id, userData);
      if (!success) {
        return rejectWithValue('Failed to update user');
      }
      return { id, ...userData };
    } catch (error) {
      return rejectWithValue('Failed to update user');
    }
  }
);

export const deleteUser = createAsyncThunk(
  'users/deleteUser',
  async (id: string, { rejectWithValue }) => {
    try {
      const success = await deleteUserAPI(id);
      if (!success) {
        return rejectWithValue('Failed to delete user');
      }
      return id;
    } catch (error) {
      return rejectWithValue('Failed to delete user');
    }
  }
);

export const resetUserMFA = createAsyncThunk(
  'users/resetUserMFA',
  async (email: string, { rejectWithValue }) => {
    try {
      const success = await resetUserMFAAPI(email);
      if (!success) {
        return rejectWithValue('Failed to reset authentication');
      }
      return email;
    } catch (error) {
      return rejectWithValue('Failed to reset authentication');
    }
  }
);

// Create the slice
const userSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    clearUserError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch users
      .addCase(fetchUsers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users = action.payload;

        // Separate users by type
        state.internalUsers = action.payload.filter(user => user.type === 'Internal');
        state.externalUsers = action.payload.filter(user => user.type === 'External');
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Create user
      .addCase(createUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users.push(action.payload);

        // Add to the appropriate list based on type
        if (action.payload.type === 'Internal') {
          state.internalUsers.push(action.payload);
        } else {
          state.externalUsers.push(action.payload);
        }
      })
      .addCase(createUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Create user with specific format
      .addCase(createUserWithSpecificFormat.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createUserWithSpecificFormat.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users.push(action.payload);

        // Add to the appropriate list based on type
        if (action.payload.type === 'Internal') {
          state.internalUsers.push(action.payload);
        } else {
          state.externalUsers.push(action.payload);
        }
      })
      .addCase(createUserWithSpecificFormat.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Update user
      .addCase(updateUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.isLoading = false;
        const { id, ...updatedData } = action.payload;

        // Update in the main users array
        const userIndex = state.users.findIndex(user => user.id === id);
        if (userIndex !== -1) {
          state.users[userIndex] = { ...state.users[userIndex], ...updatedData };

          // If type is changing, we need to move the user between arrays
          if (updatedData.type) {
            // Recategorize all users to be safe
            state.internalUsers = state.users.filter(user => user.type === 'Internal');
            state.externalUsers = state.users.filter(user => user.type === 'External');
          } else {
            // Just update the user in the appropriate array
            const internalIndex = state.internalUsers.findIndex(user => user.id === id);
            if (internalIndex !== -1) {
              state.internalUsers[internalIndex] = { ...state.internalUsers[internalIndex], ...updatedData };
            }

            const externalIndex = state.externalUsers.findIndex(user => user.id === id);
            if (externalIndex !== -1) {
              state.externalUsers[externalIndex] = { ...state.externalUsers[externalIndex], ...updatedData };
            }
          }
        }
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Update user with specific URL
      .addCase(updateUserWithSpecificUrl.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUserWithSpecificUrl.fulfilled, (state, action) => {
        state.isLoading = false;
        const { id, ...updatedData } = action.payload;

        // Update in the main users array
        const userIndex = state.users.findIndex(user => user.id === id);
        if (userIndex !== -1) {
          state.users[userIndex] = { ...state.users[userIndex], ...updatedData };

          // If type is changing, we need to move the user between arrays
          if (updatedData.type) {
            // Recategorize all users to be safe
            state.internalUsers = state.users.filter(user => user.type === 'Internal');
            state.externalUsers = state.users.filter(user => user.type === 'External');
          } else {
            // Just update the user in the appropriate array
            const internalIndex = state.internalUsers.findIndex(user => user.id === id);
            if (internalIndex !== -1) {
              state.internalUsers[internalIndex] = { ...state.internalUsers[internalIndex], ...updatedData };
            }

            const externalIndex = state.externalUsers.findIndex(user => user.id === id);
            if (externalIndex !== -1) {
              state.externalUsers[externalIndex] = { ...state.externalUsers[externalIndex], ...updatedData };
            }
          }
        }
      })
      .addCase(updateUserWithSpecificUrl.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Delete user
      .addCase(deleteUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.isLoading = false;
        const id = action.payload;

        // Remove from all arrays
        state.users = state.users.filter(user => user.id !== id);
        state.internalUsers = state.internalUsers.filter(user => user.id !== id);
        state.externalUsers = state.externalUsers.filter(user => user.id !== id);
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Reset user MFA
      .addCase(resetUserMFA.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resetUserMFA.fulfilled, (state) => {
        state.isLoading = false;
        // No need to update state as this doesn't change any user data
      })
      .addCase(resetUserMFA.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearUserError } = userSlice.actions;

export default userSlice.reducer;
