import { api } from './apiService';
import { toast } from '@/hooks/use-toast';
import { store } from '@/store';
import { setUser, logoutSuccess } from '@/store/slices/authSlice';
import tokenService from '@/api/tokenService';
import storageService from './storageService';

// Define the user profile interface
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  roles: string[];
  // Add other properties as needed
}

// API endpoint for user profile
const USER_PROFILE_ENDPOINT = '/users/me';

/**
 * Fetch the current user's profile information
 * @returns Promise with the user profile data
 */
export const fetchUserProfile = async (): Promise<UserProfile | null> => {
  try {
    console.log('Fetching user profile from API');
    const data = await api.get<UserProfile>(USER_PROFILE_ENDPOINT);

    if (data) {
      console.log('User profile fetched successfully:', data);

      // Update the Redux store with the user profile
      store.dispatch(setUser({
        userId: data.id,
        username: data.name,
        email: data.email,
        // Map other properties as needed
      }));

      return data;
    }

    return null;
  } catch (error: any) {
    console.error('Failed to fetch user profile:', error);

    // If the error is a 401 Unauthorized, handle it
    if (error.response?.status === 401) {
      handleUnauthorized();
    } else {
      // Show a toast for other errors
      toast({
        title: 'Error',
        description: 'Failed to fetch user profile. Please try again.',
        variant: 'destructive',
      });
    }

    return null;
  }
};

/**
 * Handle unauthorized (401) responses
 * Logs out the user and redirects to login page
 */
export const handleUnauthorized = (): void => {
  console.log('Unauthorized access detected, logging out user');

  // Clear tokens from tokenService
  tokenService.clearTokens();

  // Clear tokens from localStorage
  localStorage.removeItem('access_token');
  localStorage.removeItem('id_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('token_type');
  localStorage.removeItem('expires_in');

  // Clear auth token from storage service
  storageService.removeItem('auth_token');

  // Clear Redux persist storage
  localStorage.removeItem('persist:auth');

  // Clear Cognito tokens from localStorage
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.startsWith('CognitoIdentityServiceProvider.')) {
      console.log('Removing Cognito key:', key);
      localStorage.removeItem(key);
    }
  });

  // Clear Redux state
  store.dispatch(logoutSuccess());

  // Set a flag to prevent automatic redirection back to home
  sessionStorage.setItem('manual_logout', 'true');

  // Show error message
  toast({
    title: 'Session Expired',
    description: 'Your session has expired. Please log in again.',
    variant: 'destructive',
  });

  // Redirect to login page
  setTimeout(() => {
    window.location.href = '/login';
  }, 1500);
};

export default {
  fetchUserProfile,
  handleUnauthorized
};
