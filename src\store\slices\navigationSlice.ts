import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface NavigationState {
  currentPath: string;
  previousPath: string;
}

const initialState: NavigationState = {
  currentPath: '/',
  previousPath: '/',
};

export const navigationSlice = createSlice({
  name: 'navigation',
  initialState,
  reducers: {
    setCurrentPath: (state, action: PayloadAction<string>) => {
      state.previousPath = state.currentPath;
      state.currentPath = action.payload;
    },
    resetNavigation: () => initialState,
  },
});

export const { setCurrentPath, resetNavigation } = navigationSlice.actions;

export default navigationSlice.reducer;
