import React from 'react';
import { cn } from '@/lib/utils';
import { BRAND_COLORS } from '@/lib/colors';

interface CompanyLogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const CompanyLogo: React.FC<CompanyLogoProps> = ({
  className,
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  return (
    <div className={cn('flex items-center justify-center rounded-md overflow-hidden', sizeClasses[size], className)}>
      {/* This is a placeholder logo - replace with actual company logo if available */}
      <svg 
        viewBox="0 0 40 40" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        className="w-full h-full"
      >
        <rect width="40" height="40" fill={BRAND_COLORS.blue.DEFAULT} />
        <path 
          d="M8 8L20 32H16L4 8H8Z" 
          fill="white"
        />
        <path 
          d="M20 8H36V12H24V18H34V22H24V32H20V8Z" 
          fill={BRAND_COLORS.red.DEFAULT}
        />
      </svg>
    </div>
  );
};

export default CompanyLogo;
