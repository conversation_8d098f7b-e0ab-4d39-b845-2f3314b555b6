# AcuiZen WorkHub

## Project info

**URL**: https://lovable.dev/projects/3b179d25-c40c-4261-a3fb-00e642e1d767

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/3b179d25-c40c-4261-a3fb-00e642e1d767) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- AWS Amplify/Cognito

## AWS Cognito Setup

This project uses AWS Cognito for authentication with AWS Amplify v6. Follow these steps to set up AWS Cognito:

1. **Create a User Pool in AWS Cognito**:
   - Go to the [AWS Cognito Console](https://console.aws.amazon.com/cognito/home)
   - Click "Create user pool"
   - Follow the wizard to set up your user pool
   - Configure sign-in options (email, username, etc.)
   - Configure security requirements
   - Configure sign-up experience
   - Configure message delivery
   - Integrate your app (create an app client)
   - Review and create

2. **Configure Environment Variables**:
   - Copy the `.env.example` file to `.env.local`
   - Update the following variables with your AWS Cognito details:
     ```
     VITE_AWS_REGION=your-region
     VITE_AWS_USER_POOL_ID=your-user-pool-id
     VITE_AWS_USER_POOL_WEB_CLIENT_ID=your-app-client-id
     VITE_AWS_OAUTH_DOMAIN=your-cognito-domain.auth.your-region.amazoncognito.com
     VITE_AWS_REDIRECT_SIGN_IN=http://localhost:8080
     VITE_AWS_REDIRECT_SIGN_OUT=http://localhost:8080/login

     # Login Configuration API
     VITE_LOGIN_CONFIG_API_URL=https://admin.client-api.acuizen.com/login-configs
     # CORS Proxy (if needed, leave empty if not needed)
     VITE_CORS_PROXY_URL=
     ```

   The application will fetch login configuration (including Cognito details) from the API. If the API is unavailable, it will use the following fallback mechanisms in order:

   1. The fallback configuration in `src/data/loginConfig.json`
   2. The environment variables in `.env.local`
   3. The hardcoded default values

   **Logo Handling**:

   The application will fetch the logo using a presigned URL from the API:

   1. If the logo URL in the configuration is absolute (starts with http), it will be used directly
   2. If the logo URL is a filename, the application will fetch a presigned URL from `https://admin.client-api.acuizen.com/files/LOGO_FILENAME/presigned-url`
   3. To avoid CORS issues with S3 presigned URLs, the application will:
      - Fetch the image as a blob using the presigned URL
      - Create a blob URL from the fetched image
      - Use the blob URL as the image source
   4. If the presigned URL request fails, it will fall back to a direct URL at `https://admin.client-api.acuizen.com/files/LOGO_FILENAME`
   5. Multiple fallback mechanisms are in place to ensure the logo is displayed

3. **Create a Test User**:
   - In the AWS Cognito Console, go to your User Pool
   - Click "Create user"
   - Fill in the required information
   - Set a temporary password
   - The user will need to change their password on first login

4. **Test Authentication**:
   - Start the development server: `npm run dev`
   - Navigate to the login page
   - Log in with the test user credentials
   - You'll be prompted to change your password on first login

5. **Troubleshooting**:
   - If you encounter CORS issues with the login configuration API or logo presigned URL API:
     - Use a CORS proxy by setting the `VITE_CORS_PROXY_URL` environment variable
     - Example: `VITE_CORS_PROXY_URL=https://cors-anywhere.herokuapp.com/`
     - Or use a local CORS proxy like [local-cors-proxy](https://www.npmjs.com/package/local-cors-proxy)
   - If you encounter CORS issues with Cognito, make sure your app client in Cognito has the correct callback URLs
   - If the logo doesn't appear due to CORS issues:
     - The application will automatically try to handle CORS issues by fetching the image as a blob
     - If that fails, check the browser console for error messages
     - Verify that the logo filename in the configuration is correct
     - Try using a CORS proxy by setting the `VITE_CORS_PROXY_URL` environment variable
     - Try using an absolute URL for the logo instead of a filename
     - If using a custom S3 bucket, make sure CORS is properly configured on the bucket
   - Check the browser console for detailed error messages
   - Verify that your environment variables are correctly set
   - If the login configuration API is unavailable, the application will use the fallback configuration

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/3b179d25-c40c-4261-a3fb-00e642e1d767) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
