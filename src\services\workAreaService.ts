import { api } from './apiService';
import { toast } from '@/components/ui/use-toast';

// API endpoints
const WORK_AREAS_ENDPOINT = '/departments';
const WORK_ACTIVITIES_ENDPOINT = (workAreaId: string) => `/departments/${workAreaId}/work-activities`;

// Interface definitions
export interface WorkActivity {
  id: string;
  name: string;
  workAreaId?: string; // Make optional for UI compatibility
  createdAt?: string;
  updatedAt?: string;
}

export interface WorkArea {
  id: string;
  name: string;
  createdAt?: string;
  updatedAt?: string;
  workActivities?: WorkActivity[];
  expanded?: boolean; // UI state, not from API
}

/**
 * Fetch all work areas
 * @returns Promise with array of work areas
 */
export const fetchWorkAreas = async (): Promise<WorkArea[]> => {
  try {
    console.log('Fetching work areas from API');
    const data = await api.get<WorkArea[]>(WORK_AREAS_ENDPOINT);

    // Add expanded property for UI state
    const workAreasWithUIState = data.map(area => ({
      ...area,
      expanded: false
    }));

    console.log('Work areas fetched successfully:', workAreasWithUIState);
    return workAreasWithUIState;
  } catch (error) {
    console.error('Failed to fetch work areas:', error);
    toast({
      title: 'Error',
      description: 'Failed to fetch work areas. Please try again.',
      variant: 'destructive',
    });
    return [];
  }
};

/**
 * Create a new work area
 * @param name The name of the work area to create
 * @returns Promise with the created work area
 */
export const createWorkArea = async (name: string): Promise<WorkArea | null> => {
  try {
    console.log('Creating new work area:', name);
    const response = await api.post<WorkArea>(WORK_AREAS_ENDPOINT, { name });

    console.log('Work area created successfully:', response);
    toast({
      title: 'Success',
      description: 'Work area created successfully.',
    });

    return {
      ...response,
      expanded: false,
      workActivities: []
    };
  } catch (error) {
    console.error('Failed to create work area:', error);
    toast({
      title: 'Error',
      description: 'Failed to create work area. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Update a work area
 * @param id The ID of the work area to update
 * @param name The new name for the work area
 * @returns Promise with success flag
 */
export const updateWorkArea = async (id: string, name: string): Promise<boolean> => {
  try {
    console.log(`Updating work area ${id} with name:`, name);
    await api.patch<any>(`${WORK_AREAS_ENDPOINT}/${id}`, { name });

    console.log('Work area updated successfully');
    toast({
      title: 'Success',
      description: 'Work area updated successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to update work area:', error);
    toast({
      title: 'Error',
      description: 'Failed to update work area. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Delete a work area
 * @param id The ID of the work area to delete
 * @returns Promise with success flag
 */
export const deleteWorkArea = async (id: string): Promise<boolean> => {
  try {
    console.log(`Deleting work area ${id}`);
    await api.delete(`${WORK_AREAS_ENDPOINT}/${id}`);

    console.log('Work area deleted successfully');
    toast({
      title: 'Success',
      description: 'Work area deleted successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to delete work area:', error);
    toast({
      title: 'Error',
      description: 'Failed to delete work area. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Fetch work activities for a work area
 * @param workAreaId The ID of the work area
 * @returns Promise with array of work activities
 */
export const fetchWorkActivities = async (workAreaId: string): Promise<WorkActivity[]> => {
  try {
    console.log(`Fetching work activities for work area ${workAreaId}`);
    const data = await api.get<WorkActivity[]>(WORK_ACTIVITIES_ENDPOINT(workAreaId));

    // Ensure each activity has the workAreaId
    const activitiesWithWorkAreaId = data.map(activity => ({
      ...activity,
      workAreaId
    }));

    console.log('Work activities fetched successfully:', activitiesWithWorkAreaId);
    return activitiesWithWorkAreaId;
  } catch (error) {
    console.error(`Failed to fetch work activities for work area ${workAreaId}:`, error);
    toast({
      title: 'Error',
      description: 'Failed to fetch work activities. Please try again.',
      variant: 'destructive',
    });
    return [];
  }
};

/**
 * Create a new work activity
 * @param workAreaId The ID of the parent work area
 * @param name The name of the work activity to create
 * @returns Promise with the created work activity
 */
export const createWorkActivity = async (workAreaId: string, name: string): Promise<WorkActivity | null> => {
  try {
    console.log(`Creating new work activity for work area ${workAreaId}:`, name);
    const response = await api.post<WorkActivity>(WORK_ACTIVITIES_ENDPOINT(workAreaId), { name });

    console.log('Work activity created successfully:', response);
    toast({
      title: 'Success',
      description: 'Work activity created successfully.',
    });

    // Ensure the workAreaId is included in the response
    return {
      ...response,
      workAreaId
    };
  } catch (error) {
    console.error('Failed to create work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to create work activity. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Update a work activity
 * @param workAreaId The ID of the parent work area
 * @param activityId The ID of the work activity to update
 * @param name The new name for the work activity
 * @returns Promise with success flag
 */
export const updateWorkActivity = async (workAreaId: string, activityId: string, name: string): Promise<boolean> => {
  try {
    console.log(`Updating work activity ${activityId} with name:`, name);
    await api.patch<any>(`${WORK_ACTIVITIES_ENDPOINT(workAreaId)}/${activityId}`, { name });

    console.log('Work activity updated successfully');
    toast({
      title: 'Success',
      description: 'Work activity updated successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to update work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to update work activity. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Delete a work activity
 * @param workAreaId The ID of the parent work area
 * @param activityId The ID of the work activity to delete
 * @returns Promise with success flag
 */
export const deleteWorkActivity = async (workAreaId: string, activityId: string): Promise<boolean> => {
  try {
    console.log(`Deleting work activity ${activityId} from work area ${workAreaId}`);
    await api.delete(`${WORK_ACTIVITIES_ENDPOINT(workAreaId)}/${activityId}`);

    console.log('Work activity deleted successfully');
    toast({
      title: 'Success',
      description: 'Work activity deleted successfully.',
    });

    return true;
  } catch (error) {
    console.error('Failed to delete work activity:', error);
    toast({
      title: 'Error',
      description: 'Failed to delete work activity. Please try again.',
      variant: 'destructive',
    });
    return false;
  }
};
