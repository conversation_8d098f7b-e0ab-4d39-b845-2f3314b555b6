import { useState, useEffect } from "react";
import { api } from "@/services/apiService";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";

// Define URL constants with base URL
const BASE_URL = "https://admin.client-api.acuizen.com";
const LOCATION1_URL = `${BASE_URL}/location-ones`;
const LOCATION_TIER1_URL = (id: string) => `${BASE_URL}/location-ones/${id}/location-twos`;
const TIER1_TIER2_URL = (id: string) => `${BASE_URL}/location-twos/${id}/location-threes`;
const TIER2_TIER3_URL = (id: string) => `${BASE_URL}/location-threes/${id}/location-fours`;
const TIER3_TIER4_URL = (id: string) => `${BASE_URL}/location-fours/${id}/location-fives`;
const DYNAMIC_TITLES_URL = `${BASE_URL}/dynamic-titles`

// Define interfaces
interface LocationItem {
  id: string;
  name: string;
}

interface LocationTitles {
  tier1: string;
  tier2: string;
  tier3: string;
  tier4: string;
  tier5: string;
}

interface FilterLocationProps {
  handleFilter: (tier1: string, tier2: string, tier3: string, tier4: string, tier5: string) => void;
  disableAll?: boolean;
  onSubLevelsAvailable?: (hasSubLevels: {
    tier2Available: boolean;
    tier3Available: boolean;
    tier4Available: boolean;
    tier5Available: boolean;
  }) => void;
}

export function FilterLocation({ handleFilter, disableAll = false, onSubLevelsAvailable }: FilterLocationProps) {
  // State for location data
  const [locationOne, setLocationOne] = useState<LocationItem[]>([]);
  const [locationTwo, setLocationTwo] = useState<LocationItem[]>([]);
  const [locationThree, setLocationThree] = useState<LocationItem[]>([]);
  const [locationFour, setLocationFour] = useState<LocationItem[]>([]);
  const [locationFive, setLocationFive] = useState<LocationItem[]>([]);

  // State for selected locations
  const [selectedLocationOne, setSelectedLocationOne] = useState<string>('all-tier1');
  const [selectedLocationTwo, setSelectedLocationTwo] = useState<string>('all-tier2');
  const [selectedLocationThree, setSelectedLocationThree] = useState<string>('all-tier3');
  const [selectedLocationFour, setSelectedLocationFour] = useState<string>('all-tier4');
  const [selectedLocationFive, setSelectedLocationFive] = useState<string>('all-tier5');

  // State for loading indicators
  const [isLoadingTier1, setIsLoadingTier1] = useState<boolean>(false);
  const [isLoadingTier2, setIsLoadingTier2] = useState<boolean>(false);
  const [isLoadingTier3, setIsLoadingTier3] = useState<boolean>(false);
  const [isLoadingTier4, setIsLoadingTier4] = useState<boolean>(false);
  const [isLoadingTier5, setIsLoadingTier5] = useState<boolean>(false);
  const [isLoadingTitles, setIsLoadingTitles] = useState<boolean>(false);

  // State for dynamic titles
  const [titles, setTitles] = useState<LocationTitles>({
    tier1: 'Country',
    tier2: 'Region',
    tier3: 'Site',
    tier4: 'Level',
    tier5: 'Sublevel'
  });

  // Fetch tier 1 locations on component mount
  useEffect(() => {
    getLocationOne();
    getLocationConfigs();
  }, []);

  // Fetch tier 2 locations when tier 1 selection changes
  useEffect(() => {
    const fetchLocationTwo = async () => {
      if (!selectedLocationOne) return;

      setIsLoadingTier2(true);
      try {
        // Use the actual API endpoint
        const response = await fetch(LOCATION_TIER1_URL(selectedLocationOne));

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        setLocationTwo(data);

        // Only set data if it exists and has items
        if (!data || data.length === 0) {
          setLocationTwo([]);
        }
      } catch (error) {
        console.error("Failed to fetch tier 2 locations:", error);
        setLocationTwo([]);
      } finally {
        setIsLoadingTier2(false);
      }
    };

    if (selectedLocationOne && !['tier1-all', 'all-tier1'].includes(selectedLocationOne)) {
      fetchLocationTwo();
    } else {
      setSelectedLocationTwo('all-tier2');
      setSelectedLocationThree('all-tier3');
      setSelectedLocationFour('all-tier4');
      setLocationTwo([]);
      setLocationThree([]);
      setLocationFour([]);
    }
  }, [selectedLocationOne]);

  // Fetch tier 3 locations when tier 2 selection changes
  useEffect(() => {
    const fetchLocationThree = async () => {
      if (!selectedLocationTwo) return;

      setIsLoadingTier3(true);
      try {
        // Use the actual API endpoint
        const response = await fetch(TIER1_TIER2_URL(selectedLocationTwo));

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        setLocationThree(data);

        // Only set data if it exists and has items
        if (!data || data.length === 0) {
          setLocationThree([]);
        }
      } catch (error) {
        console.error("Failed to fetch tier 3 locations:", error);
        setLocationThree([]);
      } finally {
        setIsLoadingTier3(false);
      }
    };

    if (selectedLocationTwo && !['tier2-all', 'all-tier2'].includes(selectedLocationTwo)) {
      fetchLocationThree();
    } else {
      setSelectedLocationThree('all-tier3');
      setSelectedLocationFour('all-tier4');
      setLocationThree([]);
      setLocationFour([]);
    }
  }, [selectedLocationTwo]);

  // Fetch tier 4 locations when tier 3 selection changes
  useEffect(() => {
    const fetchLocationFour = async () => {
      if (!selectedLocationThree) return;

      setIsLoadingTier4(true);
      try {
        // Use the actual API endpoint
        const response = await fetch(TIER2_TIER3_URL(selectedLocationThree));

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        setLocationFour(data);

        // Only set data if it exists and has items
        if (!data || data.length === 0) {
          setLocationFour([]);
        }
      } catch (error) {
        console.error("Failed to fetch tier 4 locations:", error);
        setLocationFour([]);
      } finally {
        setIsLoadingTier4(false);
      }
    };

    if (selectedLocationThree && !['tier3-all', 'all-tier3'].includes(selectedLocationThree)) {
      fetchLocationFour();
    } else {
      setSelectedLocationFour('all-tier4');
      setSelectedLocationFive('all-tier5');
      setLocationFour([]);
      setLocationFive([]);
    }
  }, [selectedLocationThree]);

  // Fetch tier 5 locations when tier 4 selection changes
  useEffect(() => {
    const fetchLocationFive = async () => {
      if (!selectedLocationFour) return;

      setIsLoadingTier5(true);
      try {
        // Use the actual API endpoint
        const response = await fetch(TIER3_TIER4_URL(selectedLocationFour));

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        setLocationFive(data);

        // Only set data if it exists and has items
        if (!data || data.length === 0) {
          setLocationFive([]);
        }
      } catch (error) {
        console.error("Failed to fetch tier 5 locations:", error);
        setLocationFive([]);
      } finally {
        setIsLoadingTier5(false);
      }
    };

    if (selectedLocationFour && !['tier4-all', 'all-tier4'].includes(selectedLocationFour)) {
      fetchLocationFive();
    } else {
      setSelectedLocationFive('all-tier5');
      setLocationFive([]);
    }
  }, [selectedLocationFour]);

  // Update parent component when selections change
  useEffect(() => {
    // Convert "all-tier*" values to empty strings for the parent component
    const tier1 = ['all-tier1', ''].includes(selectedLocationOne) ? '' : selectedLocationOne;
    const tier2 = ['all-tier2', ''].includes(selectedLocationTwo) ? '' : selectedLocationTwo;
    const tier3 = ['all-tier3', ''].includes(selectedLocationThree) ? '' : selectedLocationThree;
    const tier4 = ['all-tier4', ''].includes(selectedLocationFour) ? '' : selectedLocationFour;
    const tier5 = ['all-tier5', ''].includes(selectedLocationFive) ? '' : selectedLocationFive;

    handleFilter(tier1, tier2, tier3, tier4, tier5);
  }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedLocationFive, handleFilter]);

  // Notify parent about sub-level availability
  useEffect(() => {
    if (onSubLevelsAvailable) {
      const hasSubLevels = {
        tier2Available: selectedLocationOne &&
                       !['tier1-all', 'all-tier1'].includes(selectedLocationOne) &&
                       (isLoadingTier2 || locationTwo.length > 0),
        tier3Available: selectedLocationTwo &&
                       !['tier2-all', 'all-tier2'].includes(selectedLocationTwo) &&
                       (isLoadingTier3 || locationThree.length > 0),
        tier4Available: selectedLocationThree &&
                       !['tier3-all', 'all-tier3'].includes(selectedLocationThree) &&
                       (isLoadingTier4 || locationFour.length > 0),
        tier5Available: selectedLocationFour &&
                       !['tier4-all', 'all-tier4'].includes(selectedLocationFour) &&
                       (isLoadingTier5 || locationFive.length > 0)
      };

      onSubLevelsAvailable(hasSubLevels);
    }
  }, [
    selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour,
    isLoadingTier2, isLoadingTier3, isLoadingTier4, isLoadingTier5,
    locationTwo.length, locationThree.length, locationFour.length, locationFive.length,
    onSubLevelsAvailable
  ]);

  // Fetch tier 1 locations
  const getLocationOne = async () => {
    setIsLoadingTier1(true);
    try {
      // Use the actual API endpoint
      const response = await fetch(LOCATION1_URL);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      setLocationOne(data);

      // Only set data if it exists and has items
      if (!data || data.length === 0) {
        setLocationOne([]);
      }
    } catch (error) {
      console.error("Failed to fetch tier 1 locations:", error);
      setLocationOne([]);
    } finally {
      setIsLoadingTier1(false);
    }
  };

  // Note: The standalone fetch functions have been moved into the useEffect hooks

  // Fetch dynamic titles
  const getLocationConfigs = async () => {
    setIsLoadingTitles(true);
    try {
      // Use the actual API endpoint
      const response = await fetch(DYNAMIC_TITLES_URL);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();

      // Define interface for the expected API response
      interface TitleItem {
        title: string;
        altTitle: string;
        [key: string]: unknown;
      }

      // If API returns data in the expected format
      if (data && Array.isArray(data)) {
        const titlesObject = data.reduce((obj: Record<string, string>, item: unknown) => {
          // Type guard to check if item has the expected structure
          const titleItem = item as Partial<TitleItem>;
          if (typeof titleItem.title === 'string' && typeof titleItem.altTitle === 'string') {
            obj[titleItem.title] = titleItem.altTitle;
          }
          return obj;
        }, {});

        setTitles({
          tier1: titlesObject.LocationOne || "Country",
          tier2: titlesObject.LocationTwo || "Region",
          tier3: titlesObject.LocationThree || "Site",
          tier4: titlesObject.LocationFour || "Level",
          tier5: titlesObject.LocationFive || "Sublevel"
        });
      } else {
        // Fallback to default titles if data format is unexpected
        setTitles({
          tier1: "Country",
          tier2: "Region",
          tier3: "Site",
          tier4: "Level",
          tier5: "Sublevel"
        });
      }
    } catch (error) {
      console.error("Failed to fetch location titles:", error);

      // Fallback to default titles on error
      setTitles({
        tier1: "Country",
        tier2: "Region",
        tier3: "Site",
        tier4: "Level",
        tier5: "Sublevel"
      });
    } finally {
      setIsLoadingTitles(false);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {/* Tier 1 Selection */}
      <div>
        <Label className="mb-2 block">{isLoadingTitles ? "Loading..." : `Choose ${titles.tier1}`}</Label>
        <Select
          value={selectedLocationOne}
          onValueChange={setSelectedLocationOne}
          disabled={isLoadingTier1}
        >
          <SelectTrigger>
            <SelectValue placeholder={`${disableAll ? 'Choose' : 'All'} ${titles.tier1}`} />
          </SelectTrigger>
          <SelectContent>
            {/* <SelectItem value="all-tier1">{disableAll ? 'Choose' : 'All'} {titles.tier1}</SelectItem> */}
            {isLoadingTier1 ? (
              <div className="flex items-center justify-center p-2">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>Loading...</span>
              </div>
            ) : locationOne.length > 0 ? (
              <>
                <SelectItem value="tier1-all">All {titles.tier1}</SelectItem>
                {locationOne.map(item => (
                  <SelectItem key={item.id} value={item.id}>
                    {item.name}
                  </SelectItem>
                ))}
              </>
            ) : (
              <div className="p-2 text-center text-gray-500">No {titles.tier1} options available</div>
            )}
          </SelectContent>
        </Select>
      </div>

      {/* Tier 2 Selection - Only show if Tier 1 is selected and there are options */}
      {selectedLocationOne &&
       !['tier1-all', 'all-tier1'].includes(selectedLocationOne) &&
       (isLoadingTier2 || locationTwo.length > 0) && (
        <div>
          <Label className="mb-2 block">{isLoadingTitles ? "Loading..." : `Choose ${titles.tier2}`}</Label>
          <Select
            value={selectedLocationTwo}
            onValueChange={setSelectedLocationTwo}
            disabled={isLoadingTier2}
          >
            <SelectTrigger>
              <SelectValue placeholder={`${disableAll ? 'Choose' : 'All'} ${titles.tier2}`} />
            </SelectTrigger>
            <SelectContent>
              {/* <SelectItem value="all-tier2">{disableAll ? 'Choose' : 'All'} {titles.tier2}</SelectItem> */}
              {isLoadingTier2 ? (
                <div className="flex items-center justify-center p-2">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span>Loading...</span>
                </div>
              ) : locationTwo.length > 0 ? (
                <>
                  <SelectItem value="tier2-all">All {titles.tier2}</SelectItem>
                  {locationTwo.map(item => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name}
                    </SelectItem>
                  ))}
                </>
              ) : (
                <div className="p-2 text-center text-gray-500">No {titles.tier2} options available</div>
              )}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Tier 3 Selection - Only show if Tier 2 is selected and there are options */}
      {selectedLocationTwo &&
       !['tier2-all', 'all-tier2'].includes(selectedLocationTwo) &&
       (isLoadingTier3 || locationThree.length > 0) && (
        <div>
          <Label className="mb-2 block">{isLoadingTitles ? "Loading..." : `Choose ${titles.tier3}`}</Label>
          <Select
            value={selectedLocationThree}
            onValueChange={setSelectedLocationThree}
            disabled={isLoadingTier3}
          >
            <SelectTrigger>
              <SelectValue placeholder={`${disableAll ? 'Choose' : 'All'} ${titles.tier3}`} />
            </SelectTrigger>
            <SelectContent>
              {/* <SelectItem value="all-tier3">{disableAll ? 'Choose' : 'All'} {titles.tier3}</SelectItem> */}
              {isLoadingTier3 ? (
                <div className="flex items-center justify-center p-2">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span>Loading...</span>
                </div>
              ) : locationThree.length > 0 ? (
                <>
                  <SelectItem value="tier3-all">All {titles.tier3}</SelectItem>
                  {locationThree.map(item => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name}
                    </SelectItem>
                  ))}
                </>
              ) : (
                <div className="p-2 text-center text-gray-500">No {titles.tier3} options available</div>
              )}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Tier 4 Selection - Only show if Tier 3 is selected and there are options */}
      {selectedLocationThree &&
       !['tier3-all', 'all-tier3'].includes(selectedLocationThree) &&
       (isLoadingTier4 || locationFour.length > 0) && (
        <div>
          <Label className="mb-2 block">{isLoadingTitles ? "Loading..." : `Choose ${titles.tier4}`}</Label>
          <Select
            value={selectedLocationFour}
            onValueChange={setSelectedLocationFour}
            disabled={isLoadingTier4}
          >
            <SelectTrigger>
              <SelectValue placeholder={`${disableAll ? 'Choose' : 'All'} ${titles.tier4}`} />
            </SelectTrigger>
            <SelectContent>
              {/* <SelectItem value="all-tier4">{disableAll ? 'Choose' : 'All'} {titles.tier4}</SelectItem> */}
              {isLoadingTier4 ? (
                <div className="flex items-center justify-center p-2">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span>Loading...</span>
                </div>
              ) : locationFour.length > 0 ? (
                <>
                  <SelectItem value="tier4-all">All {titles.tier4}</SelectItem>
                  {locationFour.map(item => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name}
                    </SelectItem>
                  ))}
                </>
              ) : (
                <div className="p-2 text-center text-gray-500">No {titles.tier4} options available</div>
              )}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Tier 5 Selection - Only show if Tier 4 is selected and there are options */}
      {selectedLocationFour &&
       !['tier4-all', 'all-tier4'].includes(selectedLocationFour) &&
       (isLoadingTier5 || locationFive.length > 0) && (
        <div>
          <Label className="mb-2 block">{isLoadingTitles ? "Loading..." : `Choose ${titles.tier5}`}</Label>
          <Select
            value={selectedLocationFive}
            onValueChange={setSelectedLocationFive}
            disabled={isLoadingTier5}
          >
            <SelectTrigger>
              <SelectValue placeholder={`${disableAll ? 'Choose' : 'All'} ${titles.tier5}`} />
            </SelectTrigger>
            <SelectContent>
              {/* <SelectItem value="all-tier5">{disableAll ? 'Choose' : 'All'} {titles.tier5}</SelectItem> */}
              {isLoadingTier5 ? (
                <div className="flex items-center justify-center p-2">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span>Loading...</span>
                </div>
              ) : locationFive.length > 0 ? (
                <>
                  <SelectItem value="tier5-all">All {titles.tier5}</SelectItem>
                  {locationFive.map(item => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name}
                    </SelectItem>
                  ))}
                </>
              ) : (
                <div className="p-2 text-center text-gray-500">No {titles.tier5} options available</div>
              )}
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  );
}

export default FilterLocation;
