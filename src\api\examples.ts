/**
 * API Usage Examples
 * 
 * This file contains examples of how to use the API service.
 * It's not meant to be imported or used in production code.
 */

import { apiClient, endpoints, tokenService } from './index';

// Example: Login user
export const loginUser = async (email: string, password: string) => {
  const response = await apiClient.post(endpoints.AUTH.LOGIN, { email, password });
  
  if (response.success && response.data) {
    // Save tokens to storage
    const { accessToken, refreshToken } = response.data;
    tokenService.setTokens(accessToken, refreshToken);
    return true;
  }
  
  return false;
};

// Example: Get user profile
export const getUserProfile = async () => {
  const response = await apiClient.get(endpoints.USER.PROFILE);
  
  if (response.success) {
    return response.data;
  }
  
  return null;
};

// Example: Update user profile
export const updateUserProfile = async (profileData: any) => {
  const response = await apiClient.put(endpoints.USER.UPDATE_PROFILE, profileData);
  return response.success;
};

// Example: Get list of users with pagination
export const getUsers = async (page: number = 1, limit: number = 10) => {
  const response = await apiClient.get(endpoints.USER.LIST, {
    params: { page, limit }
  });
  
  if (response.success) {
    return response.data;
  }
  
  return { users: [], total: 0 };
};

// Example: Upload user avatar
export const uploadUserAvatar = async (file: File, userId: string) => {
  const response = await apiClient.uploadFile(
    `/users/${userId}/avatar`,
    file,
    (progressEvent) => {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      console.log(`Upload progress: ${percentCompleted}%`);
    }
  );
  
  return response.success;
};

// Example: Logout user
export const logoutUser = async () => {
  // Call logout endpoint
  await apiClient.post(endpoints.AUTH.LOGOUT);
  
  // Clear tokens regardless of API response
  tokenService.clearTokens();
  
  // Redirect to login page
  window.location.href = '/login';
};

// Example: Using environment-specific endpoints
export const getEnvironmentSpecificData = async () => {
  // The base URL is automatically set based on the current environment
  const response = await apiClient.get('/environment-info');
  return response.data;
};
