import { api } from './apiService';
import storageService from './storageService';

// Define the EntityHierarchyLevel interface based on the API response
export interface EntityHierarchyLevel {
  id: string;
  title: string;
  altTitle: string;
  created: string;
  updated: string;
}

// Storage keys
const STORAGE_KEY_HIERARCHY_LEVELS = 'entity_hierarchy_levels';

// API endpoint for entity hierarchy levels
const HIERARCHY_LEVELS_ENDPOINT = '/dynamic-titles';

/**
 * Fetch entity hierarchy levels from the API
 * @returns Promise with array of entity hierarchy levels
 */
export const fetchEntityHierarchyLevels = async (): Promise<EntityHierarchyLevel[]> => {
  try {
    console.log('Fetching entity hierarchy levels from API');

    // Use our API service to fetch hierarchy levels
    const data = await api.get<EntityHierarchyLevel[]>(HIERARCHY_LEVELS_ENDPOINT);

    console.log('Entity hierarchy levels fetched successfully:', data);

    // Sort the levels based on the title (LocationOne, LocationTwo, etc.)
    // This ensures the hierarchy is displayed in the correct order
    const sortedData = [...data].sort((a, b) => {
      // Extract numbers from title if they exist (e.g., "LocationOne", "LocationTwo")
      const aMatch = a.title.match(/Location(\w+)/i);
      const bMatch = b.title.match(/Location(\w+)/i);

      if (aMatch && bMatch) {
        // Convert text numbers to actual numbers for comparison
        const aNum = convertTextNumberToNumber(aMatch[1]);
        const bNum = convertTextNumberToNumber(bMatch[1]);

        if (aNum !== null && bNum !== null) {
          return aNum - bNum;
        }
      }

      // If no numbers or extraction failed, sort alphabetically by title
      return a.title.localeCompare(b.title);
    });

    // Helper function to convert text numbers to actual numbers
    function convertTextNumberToNumber(textNumber: string): number | null {
      const numberMap: {[key: string]: number} = {
        'one': 1,
        'two': 2,
        'three': 3,
        'four': 4,
        'five': 5,
        'six': 6,
        'seven': 7,
        'eight': 8,
        'nine': 9,
        'ten': 10
      };

      const lowerText = textNumber.toLowerCase();

      if (lowerText in numberMap) {
        return numberMap[lowerText];
      }

      return null;
    }

    return sortedData;
  } catch (error) {
    console.error('Failed to fetch entity hierarchy levels:', error);

    // Return an empty array or throw an error based on your error handling strategy
    return [];
  }
};

/**
 * Get cached entity hierarchy levels
 * @returns Array of entity hierarchy levels or null if not cached
 */
export const getCachedEntityHierarchyLevels = (): EntityHierarchyLevel[] | null => {
  // Use the storage service to get cached hierarchy levels
  return storageService.getItem<EntityHierarchyLevel[]>(STORAGE_KEY_HIERARCHY_LEVELS);
};

/**
 * Cache entity hierarchy levels
 * @param levels Array of entity hierarchy levels to cache
 */
export const cacheEntityHierarchyLevels = (levels: EntityHierarchyLevel[]): void => {
  // Use the storage service to cache hierarchy levels with expiration
  storageService.setItem(STORAGE_KEY_HIERARCHY_LEVELS, levels, storageService.DEFAULT_EXPIRY);
};

/**
 * Get entity hierarchy levels with caching
 * @returns Promise with array of entity hierarchy levels
 */
export const getEntityHierarchyLevels = async (): Promise<EntityHierarchyLevel[]> => {
  // Check if we have cached hierarchy levels
  const cachedLevels = getCachedEntityHierarchyLevels();

  if (cachedLevels && cachedLevels.length > 0) {
    console.log('Using cached entity hierarchy levels');
    return cachedLevels;
  }

  // Fetch from API if no cache
  console.log('No cached entity hierarchy levels found, fetching from API');
  const levels = await fetchEntityHierarchyLevels();

  // Cache the levels if we got any
  if (levels.length > 0) {
    cacheEntityHierarchyLevels(levels);
    console.log('Entity hierarchy levels cached');
  }

  return levels;
};

// Default fallback hierarchy levels if API fails
export const defaultHierarchyLevels = [
  { id: "default-1", title: "LocationOne", altTitle: "Company", created: "", updated: "" },
  { id: "default-2", title: "LocationTwo", altTitle: "Region", created: "", updated: "" },
  { id: "default-3", title: "LocationThree", altTitle: "Country", created: "", updated: "" },
  { id: "default-4", title: "LocationFour", altTitle: "Site", created: "", updated: "" },
  { id: "default-5", title: "LocationFive", altTitle: "Area", created: "", updated: "" }
];

/**
 * Update an entity hierarchy level
 * @param id The ID of the level to update
 * @param updates The fields to update (altTitle and/or title)
 * @returns Promise with the updated entity hierarchy level or a success flag
 */
export const updateEntityHierarchyLevel = async (
  id: string,
  updates: { altTitle?: string; title?: string }
): Promise<EntityHierarchyLevel | { success: boolean } | null> => {
  try {
    console.log(`Updating entity hierarchy level ${id} with:`, updates);

    // Use our API service to update the hierarchy level
    try {
      const response = await api.patch<any>(
        `${HIERARCHY_LEVELS_ENDPOINT}/${id}`,
        updates
      );

      console.log('API response for update:', response);

      // Create a synthetic response if the API doesn't return the expected format
      let updatedLevel: EntityHierarchyLevel | { success: boolean };

      // Check if the response has the expected structure
      if (response && typeof response === 'object' && 'id' in response) {
        // The API returned a proper entity object
        updatedLevel = response as EntityHierarchyLevel;
      } else {
        // The API didn't return the expected format, but the request was successful
        // Create a synthetic success response
        updatedLevel = {
          success: true
        };

        console.log('Created synthetic success response for update');
      }

      // Update the cached levels if we have any
      const cachedLevels = getCachedEntityHierarchyLevels();
      if (cachedLevels) {
        const updatedLevels = cachedLevels.map(level =>
          level.id === id ? { ...level, ...updates } : level
        );
        cacheEntityHierarchyLevels(updatedLevels);
        console.log('Updated cached levels');
      }

      return updatedLevel;
    } catch (apiError) {
      console.error('API error during update:', apiError);

      // Check if the request was actually successful despite the error
      // Some APIs return 204 No Content which axios might treat as an error
      if (apiError && typeof apiError === 'object' && 'status' in apiError) {
        const status = (apiError as any).status;
        if (status === 204 || status === 200 || status === 201) {
          console.log('Request was successful despite error (status code:', status, ')');

          // Update the cached levels if we have any
          const cachedLevels = getCachedEntityHierarchyLevels();
          if (cachedLevels) {
            const updatedLevels = cachedLevels.map(level =>
              level.id === id ? { ...level, ...updates } : level
            );
            cacheEntityHierarchyLevels(updatedLevels);
            console.log('Updated cached levels');
          }

          return { success: true };
        }
      }

      throw apiError; // Re-throw to be caught by the outer catch
    }
  } catch (error) {
    console.error(`Failed to update entity hierarchy level ${id}:`, error);
    return null;
  }
};

export default {
  getEntityHierarchyLevels,
  fetchEntityHierarchyLevels,
  updateEntityHierarchyLevel,
  defaultHierarchyLevels
};
