import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import Logo from '@/components/common/Logo';
import CompanyLogo from '@/components/common/CompanyLogo';
import { User, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAppSelector } from '@/store/hooks';
import LanguageSelector from '@/components/common/LanguageSelector';
import { getLoginConfig, LoginConfig } from '@/services/loginConfigService';
import { getLogoUrl } from '@/utils/logoUtils';

interface HeaderProps {
  companyName?: string;
  departmentName?: string;
  className?: string;
  logoVariant?: 'default' | 'icon' | 'text';
  logoSize?: 'sm' | 'md' | 'lg';
  useAppLogo?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  companyName = 'AcuiZen WorkHub',
  departmentName,
  className,
  logoVariant = 'default',
  logoSize = 'lg',
  useAppLogo = false
}) => {
  const { user: authUser } = useAuth();
  const { user: reduxUser } = useAppSelector(state => state.auth);
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [configLoading, setConfigLoading] = useState<boolean>(true);
  const [loginConfig, setLoginConfig] = useState<LoginConfig | null>(null);

  // Use Redux user if available, otherwise fall back to auth context user
  const user = reduxUser || authUser;

  // We've moved the fetchLogoPresignedUrl function to logoUtils.ts

  // Fetch login configuration and logo URL
  useEffect(() => {
    const fetchConfigAndLogo = async () => {
      setConfigLoading(true);
      try {
        // Get the login configuration
        const config = await getLoginConfig();
        console.log('Login configuration loaded:', config);
        setLoginConfig(config);

        // Get the logo URL using our utility function
        const { logoUrl: url, isLoading } = await getLogoUrl(config, 'headerLogoUrl');
        setLogoUrl(url);
        setConfigLoading(isLoading);
      } catch (error) {
        console.error('Failed to fetch login configuration or logo:', error);

        // Try to use cached logo URL as fallback
        const cachedLogoUrl = localStorage.getItem('headerLogoUrl');
        if (cachedLogoUrl) {
          console.log('Using cached logo URL as fallback');
          setLogoUrl(cachedLogoUrl);
        }

        setConfigLoading(false);
      }
    };

    fetchConfigAndLogo();
  }, []);

  return (
    <header className={cn(
      "sticky top-0 z-40 w-full bg-white border-b border-gray-200 h-24 flex items-center px-6 shadow-sm",
      className
    )}>
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-6">
          {/* Logo */}
          <div className="flex-shrink-0">
            {configLoading ? (
              <div className="h-16 w-16 flex items-center justify-center">
                <Loader2 className="h-10 w-10 animate-spin text-primary" />
              </div>
            ) : logoUrl ? (
              <img
                src={logoUrl}
                alt="Company Logo"
                className="h-16 object-contain"
                onError={(e) => {
                  console.error('Failed to load logo image:', e);
                  // Fallback to default logo if image fails to load
                  if (useAppLogo) {
                    // Don't set logoUrl to empty to avoid infinite loop
                    // Just let the fallback below handle it
                  }
                }}
                loading="eager"
              />
            ) : useAppLogo ? (
              <Logo variant={logoVariant} size={logoSize} />
            ) : (
              <CompanyLogo size={logoSize === 'lg' ? 'lg' : logoSize === 'sm' ? 'sm' : 'md'} />
            )}
          </div>

          {/* Divider */}
          <div className="h-16 w-px bg-gray-200 mx-1"></div>

          {/* Company and Department Names */}
          <div className="flex flex-col">
            <h1 className="text-2xl font-semibold text-gray-900 leading-tight tracking-tight">
              {loginConfig?.COMPANY_NAME || companyName}
            </h1>
            <p className="text-base text-gray-600">
              {loginConfig?.COMPANY_DESCRIPTION || departmentName}
            </p>
          </div>
        </div>

        {/* Right side: Language Selector and User Profile */}
        <div className="flex items-center gap-6">
          {/* Language Selector */}
          <div className="mr-2">
            <LanguageSelector variant="ghost" size="default" />
          </div>

          {/* User Profile */}
          {user && (
            <div className="flex items-center">
              <div className="flex items-center gap-3">
                <div className="h-12 w-12 rounded-full bg-primary flex items-center justify-center text-white">
                  <User size={22} />
                </div>
                <div className="hidden md:block">
                  {/* <p className="text-sm font-medium">{user.username || user.userId}</p> */}
                  <p className="text-base font-medium">{user.email || user.signInDetails?.loginId}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
