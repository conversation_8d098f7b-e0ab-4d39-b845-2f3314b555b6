
import { useState } from "react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";
import MainLayout from "@/components/layout/MainLayout";
import PageHeader from "@/components/common/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Search, Download, Calendar as CalendarIcon, Clock, Filter } from "lucide-react";
import { usePagination } from "@/hooks/usePagination";
import TablePagination from "@/components/common/TablePagination";

interface ActivityLogItem {
  id: number;
  timestamp: string;
  user: string;
  action: string;
  module: string;
  details: string;
}

const ActivityLog = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [moduleFilter, setModuleFilter] = useState("all");
  const [dateFrom, setDateFrom] = useState<Date | undefined>(undefined);
  const [dateTo, setDateTo] = useState<Date | undefined>(undefined);

  // Time filter state
  const [fromHour, setFromHour] = useState<string | undefined>(undefined);
  const [fromMinute, setFromMinute] = useState<string | undefined>(undefined);
  const [toHour, setToHour] = useState<string | undefined>(undefined);
  const [toMinute, setToMinute] = useState<string | undefined>(undefined);

  // Filter popover state
  const [timestampFilterOpen, setTimestampFilterOpen] = useState(false);

  const activityLogs: ActivityLogItem[] = [
    { id: 1, timestamp: "2023-08-28 10:45:23", user: "John Smith", action: "User Created", module: "User Management", details: "Created user account for Sarah Williams" },
    { id: 2, timestamp: "2023-08-28 09:30:12", user: "Admin System", action: "Module Enabled", module: "Integrated Risk Assessment", details: "Risk Assessment module enabled for all users" },
    { id: 3, timestamp: "2023-08-27 16:22:45", user: "Sarah Williams", action: "Entity Updated", module: "General Configuration", details: "Updated entity 'New York Office'" },
    { id: 4, timestamp: "2023-08-27 14:15:38", user: "Robert Williams", action: "Report Generated", module: "Observation Reporting", details: "Generated monthly observation report" },
    { id: 5, timestamp: "2023-08-27 11:03:57", user: "Emily Davis", action: "Task Assigned", module: "Operational Tasks", details: "Assigned Task #452 to Michael Brown" },
    { id: 6, timestamp: "2023-08-26 17:30:42", user: "Michael Brown", action: "Permit Approved", module: "ePermit to Work", details: "Approved permit #PTW-2023-089" },
    { id: 7, timestamp: "2023-08-26 14:20:19", user: "John Smith", action: "Incident Reported", module: "Incident Investigation", details: "Reported incident #INC-2023-042" },
    { id: 8, timestamp: "2023-08-26 10:45:33", user: "Sarah Williams", action: "Document Uploaded", module: "Knowledge", details: "Uploaded document 'Safety Protocol v2'" },
    { id: 9, timestamp: "2023-08-25 16:55:27", user: "Robert Williams", action: "Inspection Completed", module: "Inspection", details: "Completed inspection #INS-2023-076" },
    { id: 10, timestamp: "2023-08-25 11:20:08", user: "Admin System", action: "System Update", module: "System", details: "System updated to version 2.4.5" },
    { id: 11, timestamp: "2023-08-24 15:40:12", user: "Emily Davis", action: "User Deactivated", module: "User Management", details: "Deactivated user account for James Wilson" },
    { id: 12, timestamp: "2023-08-24 09:30:55", user: "John Smith", action: "Settings Changed", module: "Settings", details: "Updated notification preferences" },
  ];

  // Helper function to convert string timestamp to Date object
  const parseTimestamp = (timestamp: string): Date => {
    const [datePart, timePart] = timestamp.split(' ');
    const [year, month, day] = datePart.split('-').map(Number);
    const [hour, minute, second] = timePart.split(':').map(Number);
    return new Date(year, month - 1, day, hour, minute, second);
  };

  const filteredLogs = activityLogs.filter((log) => {
    const moduleMatch = moduleFilter === "all" || log.module === moduleFilter;
    const searchMatch =
      log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(searchTerm.toLowerCase());

    // Date and time range filtering
    let dateTimeMatch = true;
    const logDate = parseTimestamp(log.timestamp);

    if (dateFrom) {
      // Create a date with the from date and optional time components
      const fromDate = new Date(dateFrom);

      // If time components are specified, use them; otherwise use beginning of day
      if (fromHour !== undefined && fromMinute !== undefined) {
        fromDate.setHours(parseInt(fromHour), parseInt(fromMinute), 0, 0);
      } else {
        fromDate.setHours(0, 0, 0, 0);
      }

      dateTimeMatch = dateTimeMatch && logDate >= fromDate;
    }

    if (dateTo) {
      // Create a date with the to date and optional time components
      const toDate = new Date(dateTo);

      // If time components are specified, use them; otherwise use end of day
      if (toHour !== undefined && toMinute !== undefined) {
        toDate.setHours(parseInt(toHour), parseInt(toMinute), 59, 999);
      } else {
        toDate.setHours(23, 59, 59, 999);
      }

      dateTimeMatch = dateTimeMatch && logDate <= toDate;
    }

    // Handle time-only filters (when date is not specified)
    if (!dateFrom && (fromHour !== undefined || fromMinute !== undefined)) {
      const hour = fromHour !== undefined ? parseInt(fromHour) : 0;
      const minute = fromMinute !== undefined ? parseInt(fromMinute) : 0;

      const logHour = logDate.getHours();
      const logMinute = logDate.getMinutes();

      // Compare time components
      if (logHour < hour || (logHour === hour && logMinute < minute)) {
        dateTimeMatch = false;
      }
    }

    if (!dateTo && (toHour !== undefined || toMinute !== undefined)) {
      const hour = toHour !== undefined ? parseInt(toHour) : 23;
      const minute = toMinute !== undefined ? parseInt(toMinute) : 59;

      const logHour = logDate.getHours();
      const logMinute = logDate.getMinutes();

      // Compare time components
      if (logHour > hour || (logHour === hour && logMinute > minute)) {
        dateTimeMatch = false;
      }
    }

    return moduleMatch && searchMatch && dateTimeMatch;
  });

  const modules = [
    "User Management",
    "Integrated Risk Assessment",
    "General Configuration",
    "Observation Reporting",
    "Operational Tasks",
    "ePermit to Work",
    "Incident Investigation",
    "Knowledge",
    "Inspection",
    "System",
    "Settings"
  ];

  // Generate hour options (00-23)
  const hourOptions = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, '0');
    return { value: hour, label: hour };
  });

  // Generate minute options (00-59)
  const minuteOptions = Array.from({ length: 60 }, (_, i) => {
    const minute = i.toString().padStart(2, '0');
    return { value: minute, label: minute };
  });

  // Function to clear time filters
  const clearTimeFilters = () => {
    setFromHour(undefined);
    setFromMinute(undefined);
    setToHour(undefined);
    setToMinute(undefined);
  };

  // Function to clear all timestamp filters
  const clearAllTimestampFilters = () => {
    setDateFrom(undefined);
    setDateTo(undefined);
    clearTimeFilters();
  };

  // Check if any timestamp filter is active
  const hasActiveTimestampFilter = dateFrom !== undefined || dateTo !== undefined ||
    fromHour !== undefined || fromMinute !== undefined ||
    toHour !== undefined || toMinute !== undefined;

  return (
    <MainLayout>
      <div className="content-container">
        <PageHeader
          title={t("activityLog.title")}
          subtitle={t("activityLog.subtitle")}
          actions={
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              {t("common.export")}
            </Button>
          }
        />

        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("activityLog.searchPlaceholder")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>

              <div className="w-full md:w-[200px]">
                <Select value={moduleFilter} onValueChange={setModuleFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder={t("activityLog.filterByModule")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t("common.all")} {t("common.module")}</SelectItem>
                    {modules.map((module) => (
                      <SelectItem key={module} value={module}>
                        {module}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Date and time filters removed from here and moved to column header */}

            <div className="rounded-md border">
              {(() => {
                const { paginatedData, currentPage, totalPages, goToPage } = usePagination({
                  data: filteredLogs,
                  itemsPerPage: 10
                });

                return (
                  <>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>
                            <div className="flex items-center gap-1">
                              {t("activityLog.timestamp")}
                              <Popover open={timestampFilterOpen} onOpenChange={setTimestampFilterOpen}>
                                <PopoverTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-6 w-6 p-0 ml-1 relative">
                                    <Filter className="h-4 w-4" />
                                    {hasActiveTimestampFilter && (
                                      <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-primary" />
                                    )}
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-4" align="start">
                                  <div className="space-y-4">
                                    <h4 className="font-medium">{t("activityLog.timestamp")} {t("common.filter")}</h4>

                                    {/* Date range selection */}
                                    <div className="space-y-2">
                                      <h5 className="text-sm font-medium">{t("common.date")} {t("common.filter")}</h5>
                                      <div className="grid gap-2">
                                        <div className="flex items-center gap-2">
                                          <span className="text-sm text-muted-foreground">{t("activityLog.fromDate")}:</span>
                                          <Popover>
                                            <PopoverTrigger asChild>
                                              <Button
                                                variant="outline"
                                                className="w-[150px] justify-start text-left font-normal"
                                                size="sm"
                                              >
                                                <CalendarIcon className="mr-2 h-3 w-3" />
                                                {dateFrom ? (
                                                  format(dateFrom, "PPP")
                                                ) : (
                                                  <span className="text-muted-foreground">{t("common.select")}</span>
                                                )}
                                              </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0" align="start">
                                              <Calendar
                                                mode="single"
                                                selected={dateFrom}
                                                onSelect={setDateFrom}
                                                initialFocus
                                              />
                                            </PopoverContent>
                                          </Popover>
                                        </div>

                                        <div className="flex items-center gap-2">
                                          <span className="text-sm text-muted-foreground">{t("activityLog.toDate")}:</span>
                                          <Popover>
                                            <PopoverTrigger asChild>
                                              <Button
                                                variant="outline"
                                                className="w-[150px] justify-start text-left font-normal"
                                                size="sm"
                                              >
                                                <CalendarIcon className="mr-2 h-3 w-3" />
                                                {dateTo ? (
                                                  format(dateTo, "PPP")
                                                ) : (
                                                  <span className="text-muted-foreground">{t("common.select")}</span>
                                                )}
                                              </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0" align="start">
                                              <Calendar
                                                mode="single"
                                                selected={dateTo}
                                                onSelect={setDateTo}
                                                initialFocus
                                              />
                                            </PopoverContent>
                                          </Popover>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Time range selection */}
                                    <div className="space-y-2">
                                      <h5 className="text-sm font-medium">{t("common.time")} {t("common.filter")}</h5>
                                      <div className="grid gap-2">
                                        <div className="flex items-center gap-2">
                                          <span className="text-sm text-muted-foreground">{t("From Time")}:</span>
                                          <div className="flex gap-1 items-center">
                                            <Select value={fromHour} onValueChange={setFromHour}>
                                              <SelectTrigger className="w-[60px]" size="sm">
                                                <SelectValue placeholder={t("activityLog.hour")} />
                                              </SelectTrigger>
                                              <SelectContent>
                                                <SelectItem value={undefined}>{t("common.all")}</SelectItem>
                                                {hourOptions.map((option) => (
                                                  <SelectItem key={`from-hour-${option.value}`} value={option.value}>
                                                    {option.label}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                            <span className="text-sm text-muted-foreground">:</span>
                                            <Select value={fromMinute} onValueChange={setFromMinute}>
                                              <SelectTrigger className="w-[60px]" size="sm">
                                                <SelectValue placeholder={t("activityLog.minute")} />
                                              </SelectTrigger>
                                              <SelectContent>
                                                <SelectItem value={undefined}>{t("common.all")}</SelectItem>
                                                {minuteOptions.map((option) => (
                                                  <SelectItem key={`from-minute-${option.value}`} value={option.value}>
                                                    {option.label}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                          </div>
                                        </div>

                                        <div className="flex items-center gap-2">
                                          <span className="text-sm text-muted-foreground">{t("To Time")}:</span>
                                          <div className="flex gap-1 items-center">
                                            <Select value={toHour} onValueChange={setToHour}>
                                              <SelectTrigger className="w-[60px]" size="sm">
                                                <SelectValue placeholder={t("activityLog.hour")} />
                                              </SelectTrigger>
                                              <SelectContent>
                                                <SelectItem value={undefined}>{t("common.all")}</SelectItem>
                                                {hourOptions.map((option) => (
                                                  <SelectItem key={`to-hour-${option.value}`} value={option.value}>
                                                    {option.label}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                            <span className="text-sm text-muted-foreground">:</span>
                                            <Select value={toMinute} onValueChange={setToMinute}>
                                              <SelectTrigger className="w-[60px]" size="sm">
                                                <SelectValue placeholder={t("activityLog.minute")} />
                                              </SelectTrigger>
                                              <SelectContent>
                                                <SelectItem value={undefined}>{t("common.all")}</SelectItem>
                                                {minuteOptions.map((option) => (
                                                  <SelectItem key={`to-minute-${option.value}`} value={option.value}>
                                                    {option.label}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Filter actions */}
                                    <div className="flex justify-between pt-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={clearAllTimestampFilters}
                                        disabled={!hasActiveTimestampFilter}
                                      >
                                        {t("common.clear")}
                                      </Button>
                                      <Button
                                        size="sm"
                                        onClick={() => setTimestampFilterOpen(false)}
                                      >
                                        {t("common.apply")}
                                      </Button>
                                    </div>
                                  </div>
                                </PopoverContent>
                              </Popover>
                            </div>
                          </TableHead>
                          <TableHead>{t("activityLog.user")}</TableHead>
                          <TableHead>{t("activityLog.action")}</TableHead>
                          <TableHead>{t("activityLog.id")}</TableHead>
                          <TableHead>{t("activityLog.module")}</TableHead>
                          <TableHead>{t("activityLog.details")}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredLogs.length > 0 ? (
                          paginatedData.map((log) => (
                            <TableRow key={log.id}>
                              <TableCell className="font-mono text-sm">{log.timestamp}</TableCell>
                              <TableCell>{log.user}</TableCell>
                              <TableCell>{log.action}</TableCell>
                              <TableCell>#{log.id}</TableCell>
                              <TableCell>{log.module}</TableCell>
                              <TableCell className="max-w-xs truncate">{log.details}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                              {t("activityLog.noLogsFound")}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                    {filteredLogs.length > 0 && (
                      <TablePagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={goToPage}
                      />
                    )}
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default ActivityLog;
