import { ReactNode, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '@/store/hooks';
import { useAuth } from '@/contexts/AuthContext';
import { fetchUserProfile } from '@/services/userProfileService';

interface ProtectedRouteProps {
  children: ReactNode;
}

/**
 * ProtectedRoute component
 *
 * Wraps routes that require authentication.
 * If the user is not authenticated, redirects to the login page.
 */
const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { isAuthenticated: reduxAuthenticated, user: reduxUser } = useAppSelector(state => state.auth);
  const { user: authUser } = useAuth();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check authentication from both Redux and Auth context
  useEffect(() => {
    const checkAuthAndFetchProfile = async () => {
      // Consider authenticated if either Redux or Auth context has a user
      const authenticated = reduxAuthenticated || !!reduxUser || !!authUser;

      if (authenticated) {
        // If authenticated but no user data in Redux, fetch user profile
        if (!reduxUser?.email) {
          console.log('User is authenticated but profile data is missing, fetching from API');
          try {
            await fetchUserProfile();
          } catch (error) {
            console.error('Failed to fetch user profile in ProtectedRoute:', error);
          }
        }
      }

      setIsAuthenticated(authenticated);
      setIsLoading(false);
    };

    checkAuthAndFetchProfile();
  }, [reduxAuthenticated, reduxUser, authUser]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, redirect to login page
  if (!isAuthenticated) {
    // Check if we have a manual logout flag (401 error)
    const manualLogout = sessionStorage.getItem('manual_logout');

    if (manualLogout === 'true') {
      // Don't save the current location for redirect after login
      console.log('Manual logout detected, redirecting to login without saving location');
      return <Navigate to="/login" replace />;
    } else {
      // Save the current location to redirect back after login
      console.log('Redirecting to login with location state:', location);
      return <Navigate to="/login" state={{ from: location }} replace />;
    }
  }

  // If authenticated, render the children
  return <>{children}</>;
};

export default ProtectedRoute;
