import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { BRAND_COLORS } from '@/lib/colors';

interface LogoProps {
  className?: string;
  variant?: 'default' | 'icon' | 'text';
  size?: 'sm' | 'md' | 'lg';
}

const Logo: React.FC<LogoProps> = ({
  className,
  variant = 'default',
  size = 'md'
}) => {
  const { t } = useTranslation();
  const sizeClasses = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl'
  };

  const blueColor = BRAND_COLORS.blue.DEFAULT;
  const redColor = BRAND_COLORS.red.DEFAULT;

  // Icon-only version
  if (variant === 'icon') {
    return (
      <div className={cn('font-bold flex items-center', sizeClasses[size], className)}>
        <span style={{ color: blueColor }}>A</span>
        <span style={{ color: redColor }}>Z</span>
      </div>
    );
  }

  // Text-only version
  if (variant === 'text') {
    return (
      <div className={cn('font-bold', sizeClasses[size], className)}>
        <span>{t('app.title')}</span>
      </div>
    );
  }

  // Default version with both icon and text
  return (
    <div className={cn('font-bold flex items-center gap-2', sizeClasses[size], className)}>
      <div className="flex items-center">
        <span style={{ color: blueColor }}>A</span>
        <span style={{ color: redColor }}>Z</span>
      </div>
      <span className="text-sidebar-foreground">{t('app.title')}</span>
    </div>
  );
};

export default Logo;
